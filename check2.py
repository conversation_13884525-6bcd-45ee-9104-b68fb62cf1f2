# import lancedb
# import json

# DB_URI = "/home/<USER>/pranjal/fizanto/tmp/lancedb"
# TABLE_NAME = "email_memory"

# def get_first_entry_preview(max_lines=5):
#     db = lancedb.connect(DB_URI)
    
#     if TABLE_NAME not in db.table_names():
#         print(f"No table '{TABLE_NAME}' found in {DB_URI}.")
#         return None
    
#     table = db.open_table(TABLE_NAME)
#     first_entry = table.search().limit(1).to_pandas()
    
#     if first_entry.empty:
#         print("No data stored in the knowledge base.")
#         return None
    
#     # Parse the payload and extract content
#     payload = json.loads(first_entry.iloc[0]['payload'])
#     content = payload.get('content', 'No content found')
    
#     # Truncate to first `max_lines` lines or a few paragraphs
#     preview = "\n".join(content.split("\n")[:max_lines])  # First 10 lines
#     # Alternatively, split by paragraphs (if separated by double newlines):
#     # preview = "\n\n".join(content.split("\n\n")[:2])  # First 2 paragraphs
    
#     print(f"Preview of first entry:\n{preview}")
#     return preview  # Return the truncated version

# if __name__ == "__main__":
#     preview = get_first_entry_preview()
#     # Use `preview` as needed (e.g., log it or send it in a notification)




from agno.agent import Agent, AgentKnowledge
from agno.vectordb.lancedb import LanceDb
from agno.embedder.fastembed import FastEmbedEmbedder



knowledge_base = AgentKnowledge(
    vector_db=LanceDb(
        uri="/home/<USER>/pranjal/fizanto/tmp/lancedb",
        table_name="email_memory",
        embedder=FastEmbedEmbedder(id="BAAI/bge-small-en-v1.5")
    )
)

vector_db = knowledge_base.vector_db
embedder = vector_db.embedder
query_text = ""
query_embedding = embedder.embed(query_text)[0]  # Get the first embedding (list of vectors)

results = vector_db.search(
    query=query_embedding,  # Use the embedding as the query
    limit=1
)

if results and len(results) > 0:
    first_entry = results[0].get("text", results[0].get("content", str(results[0])))  # Adjust based on actual structure
        print(f"First entry: {first_entry}")
else:
    print("No entries found in the knowledge base.")
except Exception as e:
    print(f"Error retrieving first entry: {e}")