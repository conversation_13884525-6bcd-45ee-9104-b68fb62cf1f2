{% extends "base.html" %}

{% block content %}
<div class="section" id="supporting-data">
    <h2>7. Supporting Data</h2>
    
    <div class="subsection" id="performance-metrics-by-segment">
        <h3>7.1 Performance Metrics by Segment</h3>
        
        <table>
            <thead>
                <tr>
                    <th>Segment</th>
                    <th>AUC-ROC</th>
                    <th>Precision</th>
                    <th>Recall</th>
                </tr>
            </thead>
            <tbody>
                {% for segment in supporting_data.performance_metrics_by_segment %}
                <tr>
                    <td>{{ segment.segment }}</td>
                    <td>{{ segment.auc_roc }}</td>
                    <td>{{ segment.precision }}</td>
                    <td>{{ segment.recall }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="chart">
            <img src="{{ charts.performance_by_segment }}" alt="Performance by Segment">
            <div class="chart-caption">Figure 7.1: Performance Metrics by Segment</div>
        </div>
        
        <p>{{ supporting_data_analysis.segment_performance }}</p>
    </div>
    
    <div class="subsection" id="calibration-analysis">
        <h3>7.2 Calibration Analysis</h3>
        
        <p>Hosmer-Lemeshow test p-value: {{ supporting_data.calibration_analysis.hosmer_lemeshow_p_value }}</p>
        
        <h4>Expected vs. Observed Default Rate by Decile</h4>
        <table>
            <thead>
                <tr>
                    <th>Decile</th>
                    <th>Expected vs. Observed</th>
                </tr>
            </thead>
            <tbody>
                {% for decile in supporting_data.calibration_analysis.decile_analysis %}
                <tr>
                    <td>{{ decile.decile }}</td>
                    <td>{{ decile.values }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="chart">
            <img src="{{ charts.calibration }}" alt="Calibration Chart">
            <div class="chart-caption">Figure 7.2: Expected vs. Observed Default Rate by Decile</div>
        </div>
        
        <p>{{ supporting_data_analysis.calibration }}</p>
    </div>
    
    <div class="subsection" id="feature-importance">
        <h3>7.3 Feature Importance</h3>
        
        <table>
            <thead>
                <tr>
                    <th>Feature</th>
                    <th>Importance (%)</th>
                </tr>
            </thead>
            <tbody>
                {% for feature in supporting_data.feature_importance %}
                <tr>
                    <td>{{ feature.feature }}</td>
                    <td>{{ feature.importance }}%</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="chart">
            <img src="{{ charts.feature_importance }}" alt="Feature Importance">
            <div class="chart-caption">Figure 7.3: Feature Importance</div>
        </div>
        
        <p>{{ supporting_data_analysis.feature_importance }}</p>
    </div>
    
    <div class="subsection" id="backtesting-results">
        <h3>7.4 Backtesting Results</h3>
        
        <table>
            <thead>
                <tr>
                    <th>Period</th>
                    <th>Predicted Default Rate</th>
                    <th>Actual Default Rate</th>
                    <th>Difference</th>
                </tr>
            </thead>
            <tbody>
                {% for result in supporting_data.backtesting_results %}
                <tr>
                    <td>{{ result.period }}</td>
                    <td>{{ result.predicted }}%</td>
                    <td>{{ result.actual }}%</td>
                    <td>{{ (result.predicted - result.actual)|abs }}%</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="chart">
            <img src="{{ charts.backtesting }}" alt="Backtesting Results">
            <div class="chart-caption">Figure 7.4: Predicted vs. Actual Default Rate by Period</div>
        </div>
        
        <div>{{ supporting_data_analysis.backtesting | safe }}</div>
    </div>
    
    <div class="subsection" id="regulatory-compliance">
        <h3>7.5 Regulatory Compliance Checks</h3>
        
        <table>
            <thead>
                <tr>
                    <th>Compliance Check</th>
                    <th>Result</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>SR 11-7 Compliance</td>
                    <td>{{ supporting_data.regulatory_compliance_checks.sr_11_7_compliance }}</td>
                </tr>
                <tr>
                    <td>Fair Lending Analysis</td>
                    <td>{{ supporting_data.regulatory_compliance_checks.fair_lending_analysis }}</td>
                </tr>
                <tr>
                    <td>CECL Alignment</td>
                    <td>{{ supporting_data.regulatory_compliance_checks.cecl_alignment }}</td>
                </tr>
                <tr>
                    <td>Model Risk Tiering</td>
                    <td>{{ supporting_data.regulatory_compliance_checks.model_risk_tiering }}</td>
                </tr>
                <tr>
                    <td>Documentation Completeness</td>
                    <td>{{ supporting_data.regulatory_compliance_checks.documentation_completeness }}</td>
                </tr>
            </tbody>
        </table>
        
        <p>{{ supporting_data_analysis.regulatory_compliance }}</p>
    </div>
</div>
{% endblock %}
