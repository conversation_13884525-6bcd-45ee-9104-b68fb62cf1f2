{% extends "base.html" %}

{% block content %}
<div class="section" id="validation-results">
    <h2>4. Validation Results</h2>
    
    <div class="subsection" id="statistical-performance">
        <h3>4.1 Statistical Performance</h3>
        
        <h4>Performance Metrics</h4>
        <table>
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Training</th>
                    <th>Validation</th>
                    <th>Testing</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>AUC-ROC</td>
                    <td>{{ validation_tests.statistical_performance.auc_roc.training }}</td>
                    <td>{{ validation_tests.statistical_performance.auc_roc.validation }}</td>
                    <td>{{ validation_tests.statistical_performance.auc_roc.testing }}</td>
                </tr>
                <tr>
                    <td>Gini Coefficient</td>
                    <td>{{ validation_tests.statistical_performance.gini_coefficient.training }}</td>
                    <td>{{ validation_tests.statistical_performance.gini_coefficient.validation }}</td>
                    <td>{{ validation_tests.statistical_performance.gini_coefficient.testing }}</td>
                </tr>
                <tr>
                    <td>KS Statistic</td>
                    <td>{{ validation_tests.statistical_performance.ks_statistic.training }}</td>
                    <td>{{ validation_tests.statistical_performance.ks_statistic.validation }}</td>
                    <td>{{ validation_tests.statistical_performance.ks_statistic.testing }}</td>
                </tr>
                <tr>
                    <td>Precision</td>
                    <td>{{ validation_tests.statistical_performance.precision.training }}</td>
                    <td>{{ validation_tests.statistical_performance.precision.validation }}</td>
                    <td>{{ validation_tests.statistical_performance.precision.testing }}</td>
                </tr>
                <tr>
                    <td>Recall</td>
                    <td>{{ validation_tests.statistical_performance.recall.training }}</td>
                    <td>{{ validation_tests.statistical_performance.recall.validation }}</td>
                    <td>{{ validation_tests.statistical_performance.recall.testing }}</td>
                </tr>
                <tr>
                    <td>F1 Score</td>
                    <td>{{ validation_tests.statistical_performance.f1_score.training }}</td>
                    <td>{{ validation_tests.statistical_performance.f1_score.validation }}</td>
                    <td>{{ validation_tests.statistical_performance.f1_score.testing }}</td>
                </tr>
                <tr>
                    <td>Brier Score</td>
                    <td>{{ validation_tests.statistical_performance.brier_score.training }}</td>
                    <td>{{ validation_tests.statistical_performance.brier_score.validation }}</td>
                    <td>{{ validation_tests.statistical_performance.brier_score.testing }}</td>
                </tr>
            </tbody>
        </table>
        
        <div class="chart">
            <img src="{{ charts.roc_curve }}" alt="ROC Curve">
            <div class="chart-caption">Figure 4.1: ROC Curve</div>
        </div>
        
        <h4>Confusion Matrix</h4>
        <table>
            <tr>
                <th></th>
                <th>Predicted Positive</th>
                <th>Predicted Negative</th>
            </tr>
            <tr>
                <th>Actual Positive</th>
                <td>{{ supporting_data.confusion_matrix.true_positives }}</td>
                <td>{{ supporting_data.confusion_matrix.false_negatives }}</td>
            </tr>
            <tr>
                <th>Actual Negative</th>
                <td>{{ supporting_data.confusion_matrix.false_positives }}</td>
                <td>{{ supporting_data.confusion_matrix.true_negatives }}</td>
            </tr>
        </table>
        
        <p>{{ validation_results.statistical_performance_analysis }}</p>
    </div>
    
    <div class="subsection" id="stability-analysis">
        <h3>4.2 Stability Analysis</h3>
        
        <h4>Population Stability</h4>
        <p>Population Stability Index (PSI): {{ validation_tests.stability_analysis.psi }}</p>
        
        <h4>Characteristic Stability</h4>
        <p>Characteristic Stability Index (CSI) Range: {{ validation_tests.stability_analysis.csi_range }}</p>
        
        <h4>Feature Importance Stability</h4>
        <p>{{ validation_tests.stability_analysis.feature_importance_stability }}</p>
        
        <h4>Temporal Performance</h4>
        <p>{{ validation_tests.stability_analysis.temporal_performance }}</p>
        
        <p>{{ validation_results.stability_analysis }}</p>
    </div>
    
    <div class="subsection" id="sensitivity-analysis">
        <h3>4.3 Sensitivity Analysis</h3>
        
        <h4>Most Sensitive Features</h4>
        <ul>
            {% for feature in validation_tests.sensitivity_analysis.most_sensitive_features %}
            <li>{{ feature.feature }}: {{ feature.impact }}</li>
            {% endfor %}
        </ul>
        
        <h4>Least Sensitive Features</h4>
        <ul>
            {% for feature in validation_tests.sensitivity_analysis.least_sensitive_features %}
            <li>{{ feature.feature }}: {{ feature.impact }}</li>
            {% endfor %}
        </ul>
        
        <h4>Stress Testing Results</h4>
        <p>{{ validation_tests.sensitivity_analysis.stress_testing_results | markdown | safe }}</p>
        
        <h4>Extreme Scenario Impact</h4>
        <p>{{ validation_tests.sensitivity_analysis.extreme_scenario_impact | markdown | safe }}</p>
        
        <div>{{ validation_results.sensitivity_analysis | safe }}</div>
    </div>
    
    <div class="subsection" id="benchmarking">
        <h3>4.4 Benchmarking</h3>
        
        <h4>Comparison with Previous Version</h4>
        <p>{{ validation_tests.benchmarking.previous_version }}</p>
        
        <h4>Industry Average</h4>
        <p>{{ validation_tests.benchmarking.industry_average }}</p>
        
        <h4>Alternative Models</h4>
        <p>{{ validation_tests.benchmarking.alternative_models }}</p>
        
        <p>{{ validation_results.benchmarking_analysis }}</p>
    </div>
    
    <div class="subsection" id="conceptual-soundness">
        <h3>4.5 Conceptual Soundness</h3>
        
        <h4>Methodology Alignment</h4>
        <p>{{ validation_tests.conceptual_soundness.methodology_alignment }}</p>
        
        <h4>Feature Relationships</h4>
        <p>{{ validation_tests.conceptual_soundness.feature_relationships }}</p>
        
        <h4>Model Complexity</h4>
        <p>{{ validation_tests.conceptual_soundness.model_complexity }}</p>
        
        <h4>Assumptions Validation</h4>
        <p>{{ validation_tests.conceptual_soundness.assumptions_validation }}</p>
        
        <p>{{ validation_results.conceptual_soundness_analysis }}</p>
    </div>
</div>
{% endblock %}
