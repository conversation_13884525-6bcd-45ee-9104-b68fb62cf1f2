<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Model Risk Management Validation Report</title>
    <style>
        @page {
            size: letter;
            margin: 1in;
            @top-center {
                content: "{{ title }}";
                font-size: 10pt;
                color: #666;
            }
            @bottom-center {
                content: "Page " counter(page) " of " counter(pages);
                font-size: 10pt;
                color: #666;
            }
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.5;
            color: #333;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Arial', sans-serif;
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        
        h1 {
            font-size: 24pt;
            text-align: center;
            margin-top: 2in;
            margin-bottom: 1in;
            page-break-before: always;
            page-break-after: always;
        }
        
        h2 {
            font-size: 18pt;
            border-bottom: 1px solid #ddd;
            padding-bottom: 0.2em;
            page-break-after: avoid;
        }
        
        h3 {
            font-size: 14pt;
            page-break-after: avoid;
        }
        
        h4 {
            font-size: 12pt;
            page-break-after: avoid;
        }
        
        p {
            margin-bottom: 0.5em;
            text-align: justify;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1em;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 0.5em;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        .cover-page {
            text-align: center;
            height: 11in;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .cover-title {
            font-size: 28pt;
            font-weight: bold;
            margin-bottom: 1in;
        }
        
        .cover-subtitle {
            font-size: 18pt;
            margin-bottom: 2in;
        }
        
        .cover-info {
            font-size: 14pt;
            margin-bottom: 0.5em;
        }
        
        .toc {
            page-break-before: always;
        }
        
        .toc h2 {
            text-align: center;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin-bottom: 0.5em;
        }
        
        .toc a {
            text-decoration: none;
            color: #333;
        }
        
        .toc .toc-level-1 {
            font-weight: bold;
            margin-top: 1em;
        }
        
        .toc .toc-level-2 {
            padding-left: 2em;
        }
        
        .toc .toc-level-3 {
            padding-left: 4em;
        }
        
        .section {
            page-break-before: always;
        }
        
        .subsection {
            page-break-after: avoid;
        }
        
        .risk-high {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .risk-medium {
            color: #f39c12;
            font-weight: bold;
        }
        
        .risk-low {
            color: #27ae60;
            font-weight: bold;
        }
        
        .chart {
            width: 100%;
            max-width: 600px;
            margin: 1em auto;
            text-align: center;
        }
        
        .chart img {
            max-width: 100%;
        }
        
        .chart-caption {
            font-style: italic;
            text-align: center;
            margin-top: 0.5em;
        }
        
        .footer {
            text-align: center;
            font-size: 10pt;
            color: #666;
            margin-top: 2em;
        }
        
        /* Code block styling */
        pre {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 10pt;
            overflow-x: auto;
            white-space: pre-wrap;
            margin: 1em 0;
        }
        
        code {
            font-family: 'Courier New', Courier, monospace;
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 10pt;
        }
        
        /* Table styling for markdown tables */
        .markdown-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1em 0;
        }
        
        .markdown-table th, .markdown-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .markdown-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        /* Heading styling for markdown */
        .markdown-h1, .markdown-h2, .markdown-h3, .markdown-h4 {
            color: #2c3e50;
            margin-top: 1em;
            margin-bottom: 0.5em;
        }
        
        .markdown-h1 {
            font-size: 18pt;
            border-bottom: 1px solid #ddd;
        }
        
        .markdown-h2 {
            font-size: 16pt;
        }
        
        .markdown-h3 {
            font-size: 14pt;
        }
        
        .markdown-h4 {
            font-size: 12pt;
        }
    </style>
</head>
<body>
    <!-- Cover Page -->
    <div class="cover-page">
        <div class="cover-title">Model Risk Management Validation Report</div>
        <div class="cover-subtitle">{{ model_metadata.name }}</div>
        <div class="cover-info">Model ID: {{ model_metadata.id }}</div>
        <div class="cover-info">Version: {{ model_metadata.version }}</div>
        <div class="cover-info">Validation Date: {{ model_metadata.last_validation_date }}</div>
        <div class="cover-info">Prepared by: {{ validation_team.lead_validator }}</div>
    </div>
    
    <!-- Table of Contents -->
    <div class="toc">
        <h2>Table of Contents</h2>
        <ul>
            <li class="toc-level-1"><a href="#executive-summary">1. Executive Summary</a></li>
            <li class="toc-level-1"><a href="#model-overview">2. Model Overview</a></li>
            <li class="toc-level-2"><a href="#model-purpose">2.1 Model Purpose and Use</a></li>
            <li class="toc-level-2"><a href="#model-methodology">2.2 Model Methodology</a></li>
            <li class="toc-level-2"><a href="#model-assumptions">2.3 Key Assumptions and Limitations</a></li>
            <li class="toc-level-1"><a href="#validation-approach">3. Validation Approach</a></li>
            <li class="toc-level-1"><a href="#validation-results">4. Validation Results</a></li>
            <li class="toc-level-2"><a href="#statistical-performance">4.1 Statistical Performance</a></li>
            <li class="toc-level-2"><a href="#stability-analysis">4.2 Stability Analysis</a></li>
            <li class="toc-level-2"><a href="#sensitivity-analysis">4.3 Sensitivity Analysis</a></li>
            <li class="toc-level-2"><a href="#benchmarking">4.4 Benchmarking</a></li>
            <li class="toc-level-2"><a href="#conceptual-soundness">4.5 Conceptual Soundness</a></li>
            <li class="toc-level-1"><a href="#risk-assessment">5. Risk Assessment</a></li>
            <li class="toc-level-1"><a href="#recommendations">6. Recommendations</a></li>
            <li class="toc-level-1"><a href="#supporting-data">7. Supporting Data</a></li>
            <li class="toc-level-1"><a href="#approval">8. Approval Information</a></li>
            <li class="toc-level-1"><a href="#appendix">9. Appendix</a></li>
        </ul>
    </div>
    
    <!-- Content Sections -->
    {% block content %}{% endblock %}
    
    <!-- Footer -->
    <div class="footer">
        <p>Confidential - For Internal Use Only</p>
    </div>
</body>
</html>
