{% extends "base.html" %}

{% block content %}
<div class="section" id="approval">
    <h2>8. Approval Information</h2>
    
    <h3>Validation Team</h3>
    <p><strong>Lead Validator:</strong> {{ validation_team.lead_validator }}</p>
    
    <h4>Team Members</h4>
    <ul>
        {% for member in validation_team.team_members %}
        <li>{{ member }}</li>
        {% endfor %}
    </ul>
    
    <h4>Validation Period</h4>
    <p>{{ validation_team.validation_period }}</p>
    
    <h4>Validation Scope</h4>
    <p>{{ validation_team.validation_scope }}</p>
    
    <h3>Validation Committee Decision</h3>
    <p>{{ approval_information.decision }}</p>
    
    <h4>Approval Date</h4>
    <p>{{ approval_information.approval_date }}</p>
    
    {% if approval_information.conditional_requirements %}
    <h4>Conditional Requirements</h4>
    <ol>
        {% for requirement in approval_information.conditional_requirements %}
        <li>{{ requirement }}</li>
        {% endfor %}
    </ol>
    {% endif %}
    
    <h4>Next Review Date</h4>
    <p>{{ approval_information.next_review_date }}</p>
    
    <h3>Signatures</h3>
    <table>
        <tr>
            <th>Role</th>
            <th>Name</th>
            <th>Signature</th>
            <th>Date</th>
        </tr>
        <tr>
            <td>Lead Validator</td>
            <td>{{ validation_team.lead_validator }}</td>
            <td></td>
            <td>{{ approval_information.approval_date }}</td>
        </tr>
        <tr>
            <td>Model Owner</td>
            <td>{{ model_metadata.owner }}</td>
            <td></td>
            <td>{{ approval_information.approval_date }}</td>
        </tr>
        <tr>
            <td>Model Risk Management</td>
            <td>{{ approval_information.mrm_approver }}</td>
            <td></td>
            <td>{{ approval_information.approval_date }}</td>
        </tr>
    </table>
</div>
{% endblock %}
