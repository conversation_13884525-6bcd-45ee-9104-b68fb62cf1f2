{% extends "base.html" %}

{% block content %}
<div class="section" id="executive-summary">
    <h2>1. Executive Summary</h2>
    
    <p>This report presents the findings of the independent validation of the {{ model_metadata.name }} (Model ID: {{ model_metadata.id }}, Version: {{ model_metadata.version }}), which is used by {{ model_metadata.business_unit }} for {{ model_metadata.purpose }}.</p>
    
    <h3>Validation Scope and Approach</h3>
    <p>{{ validation_team.validation_scope }}</p>
    
    <h3>Overall Assessment</h3>
    <p>Based on the comprehensive validation performed, the model has been assigned an overall risk rating of <span class="risk-{{ risk_assessment.overall_risk_rating|lower }}">{{ risk_assessment.overall_risk_rating }}</span>. This assessment reflects the model's statistical performance, conceptual soundness, and implementation quality.</p>
    
    <h3>Key Findings</h3>
    <ul>
        {% for finding in executive_summary.key_findings %}
        <li>{{ finding }}</li>
        {% endfor %}
    </ul>
    
    <h3>Statistical Performance</h3>
    <p>{{ executive_summary.statistical_performance_summary }}</p>
    
    <h3>Model Limitations</h3>
    <ul>
        {% for limitation in model_methodology.limitations %}
        <li>{{ limitation }}</li>
        {% endfor %}
    </ul>
    
    <h3>Key Recommendations</h3>
    <table>
        <thead>
            <tr>
                <th>Recommendation</th>
                <th>Owner</th>
                <th>Timeline</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            {% for recommendation in recommendations.high_priority %}
            <tr>
                <td>{{ recommendation.description }}</td>
                <td>{{ recommendation.owner }}</td>
                <td>{{ recommendation.timeline }}</td>
                <td>{{ recommendation.status }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <h3>Validation Committee Decision</h3>
    <p>{{ approval_information.decision }} on {{ approval_information.approval_date }}.</p>
    
    {% if approval_information.conditional_requirements %}
    <h4>Conditional Requirements</h4>
    <ol>
        {% for requirement in approval_information.conditional_requirements %}
        <li>{{ requirement }}</li>
        {% endfor %}
    </ol>
    {% endif %}
    
    <p>The next review is scheduled for {{ approval_information.next_review_date }}.</p>
</div>
{% endblock %}
