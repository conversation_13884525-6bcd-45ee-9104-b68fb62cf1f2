{% extends "base.html" %}

{% block content %}
<div class="section" id="model-overview">
    <h2>2. Model Overview</h2>
    
    <div class="subsection" id="model-purpose">
        <h3>2.1 Model Purpose and Use</h3>
        
        <table>
            <tr>
                <th>Model Name</th>
                <td>{{ model_metadata.name }}</td>
            </tr>
            <tr>
                <th>Model ID</th>
                <td>{{ model_metadata.id }}</td>
            </tr>
            <tr>
                <th>Version</th>
                <td>{{ model_metadata.version }}</td>
            </tr>
            <tr>
                <th>Owner</th>
                <td>{{ model_metadata.owner }}</td>
            </tr>
            <tr>
                <th>Developer</th>
                <td>{{ model_metadata.developer }}</td>
            </tr>
            <tr>
                <th>Business Unit</th>
                <td>{{ model_metadata.business_unit }}</td>
            </tr>
            <tr>
                <th>Purpose</th>
                <td>{{ model_metadata.purpose }}</td>
            </tr>
            <tr>
                <th>Implementation Date</th>
                <td>{{ model_metadata.implementation_date }}</td>
            </tr>
            <tr>
                <th>Last Validation Date</th>
                <td>{{ model_metadata.last_validation_date }}</td>
            </tr>
            <tr>
                <th>Validation Frequency</th>
                <td>{{ model_metadata.validation_frequency }}</td>
            </tr>
            <tr>
                <th>Regulatory Framework</th>
                <td>{{ model_metadata.regulatory_framework }}</td>
            </tr>
        </table>
        
        <p>{{ model_overview.purpose_description }}</p>
    </div>
    
    <div class="subsection" id="model-methodology">
        <h3>2.2 Model Methodology</h3>
        
        <h4>Model Type</h4>
        <p>{{ model_methodology.model_type }}</p>
        
        <h4>Description</h4>
        <p>{{ model_methodology.description }}</p>
        
        <h4>Data Sources</h4>
        <ul>
            {% for source in model_methodology.data_sources %}
            <li>{{ source }}</li>
            {% endfor %}
        </ul>
        
        <h4>Feature Selection</h4>
        <p>{{ model_methodology.feature_selection }}</p>
        
        <h4>Model Training</h4>
        <p>{{ model_methodology.model_training }}</p>
    </div>
    
    <div class="subsection" id="model-assumptions">
        <h3>2.3 Key Assumptions and Limitations</h3>
        
        <h4>Key Assumptions</h4>
        <ul>
            {% for assumption in model_methodology.key_assumptions %}
            <li>{{ assumption }}</li>
            {% endfor %}
        </ul>
        
        <h4>Model Limitations</h4>
        <ul>
            {% for limitation in model_methodology.limitations %}
            <li>{{ limitation }}</li>
            {% endfor %}
        </ul>
        
        <p>{{ model_overview.assumptions_limitations_analysis }}</p>
    </div>
</div>
{% endblock %}
