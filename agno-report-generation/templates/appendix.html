{% extends "base.html" %}

{% block content %}
<div class="section" id="appendix">
    <h2>9. Appendix</h2>
    
    <div class="subsection" id="glossary">
        <h3>9.1 Glossary</h3>
        
        <table>
            <thead>
                <tr>
                    <th>Term</th>
                    <th>Definition</th>
                </tr>
            </thead>
            <tbody>
                {% for term in appendix.glossary %}
                <tr>
                    <td>{{ term.term }}</td>
                    <td>{{ term.definition }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="subsection" id="methodology-details">
        <h3>9.2 Detailed Methodology</h3>
        
        <h4>Model Development Process</h4>
        <p>{{ appendix.methodology.development_process }}</p>
        
        <h4>Feature Engineering</h4>
        <p>{{ appendix.methodology.feature_engineering }}</p>
        
        <h4>Model Selection</h4>
        <p>{{ appendix.methodology.model_selection }}</p>
        
        <h4>Hyperparameter Tuning</h4>
        <p>{{ appendix.methodology.hyperparameter_tuning }}</p>
        
        <h4>Training and Validation Approach</h4>
        <p>{{ appendix.methodology.training_validation_approach }}</p>
    </div>
    
    <div class="subsection" id="data-details">
        <h3>9.3 Data Details</h3>
        
        <h4>Data Sources</h4>
        <p>{{ appendix.data.sources }}</p>
        
        <h4>Data Quality Checks</h4>
        <p>{{ appendix.data.quality_checks }}</p>
        
        <h4>Data Preprocessing</h4>
        <p>{{ appendix.data.preprocessing }}</p>
        
        <h4>Feature Distributions</h4>
        <div class="chart">
            <img src="{{ charts.feature_distributions }}" alt="Feature Distributions">
            <div class="chart-caption">Figure 9.1: Feature Distributions</div>
        </div>
    </div>
    
    <div class="subsection" id="validation-details">
        <h3>9.4 Detailed Validation Results</h3>
        
        <h4>Statistical Tests</h4>
        <p>{{ appendix.validation.statistical_tests }}</p>
        
        <h4>Sensitivity Analysis Details</h4>
        <p>{{ appendix.validation.sensitivity_analysis }}</p>
        
        <h4>Stress Testing Scenarios</h4>
        <table>
            <thead>
                <tr>
                    <th>Scenario</th>
                    <th>Description</th>
                    <th>Impact</th>
                </tr>
            </thead>
            <tbody>
                {% for scenario in appendix.validation.stress_testing_scenarios %}
                <tr>
                    <td>{{ scenario.name }}</td>
                    <td>{{ scenario.description }}</td>
                    <td>{{ scenario.impact }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="subsection" id="regulatory-details">
        <h3>9.5 Regulatory Framework Details</h3>
        
        <h4>SR 11-7 Requirements</h4>
        <p>{{ appendix.regulatory.sr_11_7 }}</p>
        
        <h4>Fair Lending Compliance</h4>
        <p>{{ appendix.regulatory.fair_lending }}</p>
        
        <h4>CECL Requirements</h4>
        <p>{{ appendix.regulatory.cecl }}</p>
    </div>
    
    <div class="subsection" id="references">
        <h3>9.6 References</h3>
        
        <ol>
            {% for reference in appendix.references %}
            <li>{{ reference }}</li>
            {% endfor %}
        </ol>
    </div>
</div>
{% endblock %}
