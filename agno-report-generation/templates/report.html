<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        @page {
            size: letter;
            margin: 1in;
            @top-center {
                content: "{{ title }}";
                font-size: 10pt;
                color: #666;
            }
            @bottom-center {
                content: "Page " counter(page) " of " counter(pages);
                font-size: 10pt;
                color: #666;
            }
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.5;
            color: #333;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Arial', sans-serif;
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        
        h1 {
            font-size: 24pt;
            text-align: center;
            margin-top: 2in;
            margin-bottom: 1in;
            page-break-before: always;
            page-break-after: always;
        }
        
        h2 {
            font-size: 18pt;
            border-bottom: 1px solid #ddd;
            padding-bottom: 0.2em;
            page-break-after: avoid;
        }
        
        h3 {
            font-size: 14pt;
            page-break-after: avoid;
        }
        
        h4 {
            font-size: 12pt;
            page-break-after: avoid;
        }
        
        p {
            margin-bottom: 0.5em;
            text-align: justify;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1em;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 0.5em;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        .cover-page {
            text-align: center;
            height: 11in;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .cover-title {
            font-size: 28pt;
            font-weight: bold;
            margin-bottom: 1in;
        }
        
        .cover-subtitle {
            font-size: 18pt;
            margin-bottom: 2in;
        }
        
        .cover-info {
            font-size: 14pt;
            margin-bottom: 0.5em;
        }
        
        .toc {
            page-break-before: always;
        }
        
        .toc h2 {
            text-align: center;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin-bottom: 0.5em;
        }
        
        .toc a {
            text-decoration: none;
            color: #333;
        }
        
        .toc .toc-level-1 {
            font-weight: bold;
            margin-top: 1em;
        }
        
        .toc .toc-level-2 {
            padding-left: 2em;
        }
        
        .toc .toc-level-3 {
            padding-left: 4em;
        }
        
        .section {
            page-break-before: always;
        }
        
        .subsection {
            page-break-after: avoid;
        }
        
        .risk-high {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .risk-medium {
            color: #f39c12;
            font-weight: bold;
        }
        
        .risk-low {
            color: #27ae60;
            font-weight: bold;
        }
        
        .chart {
            width: 100%;
            max-width: 600px;
            margin: 1em auto;
            text-align: center;
        }
        
        .chart img {
            max-width: 100%;
        }
        
        .chart-caption {
            font-style: italic;
            text-align: center;
            margin-top: 0.5em;
        }
        
        .footer {
            text-align: center;
            font-size: 10pt;
            color: #666;
            margin-top: 2em;
        }
    </style>
</head>
<body>
    <!-- Cover Page -->
    <div class="cover-page">
        <div class="cover-title">Model Risk Management Validation Report</div>
        <div class="cover-subtitle">{{ model_metadata.name }}</div>
        <div class="cover-info">Model ID: {{ model_metadata.id }}</div>
        <div class="cover-info">Version: {{ model_metadata.version }}</div>
        <div class="cover-info">Validation Date: {{ model_metadata.last_validation_date }}</div>
        <div class="cover-info">Prepared by: {{ validation_team.lead_validator }}</div>
    </div>
    
    <!-- Table of Contents -->
    <div class="toc">
        <h2>Table of Contents</h2>
        <ul>
            <li class="toc-level-1"><a href="#executive-summary">1. Executive Summary</a></li>
            <li class="toc-level-1"><a href="#model-overview">2. Model Overview</a></li>
            <li class="toc-level-2"><a href="#model-purpose">2.1 Model Purpose and Use</a></li>
            <li class="toc-level-2"><a href="#model-methodology">2.2 Model Methodology</a></li>
            <li class="toc-level-2"><a href="#model-assumptions">2.3 Key Assumptions and Limitations</a></li>
            <li class="toc-level-1"><a href="#validation-approach">3. Validation Approach</a></li>
            <li class="toc-level-1"><a href="#validation-results">4. Validation Results</a></li>
            <li class="toc-level-2"><a href="#statistical-performance">4.1 Statistical Performance</a></li>
            <li class="toc-level-2"><a href="#stability-analysis">4.2 Stability Analysis</a></li>
            <li class="toc-level-2"><a href="#sensitivity-analysis">4.3 Sensitivity Analysis</a></li>
            <li class="toc-level-2"><a href="#benchmarking">4.4 Benchmarking</a></li>
            <li class="toc-level-2"><a href="#conceptual-soundness">4.5 Conceptual Soundness</a></li>
            <li class="toc-level-1"><a href="#risk-assessment">5. Risk Assessment</a></li>
            <li class="toc-level-1"><a href="#recommendations">6. Recommendations</a></li>
            <li class="toc-level-1"><a href="#supporting-data">7. Supporting Data</a></li>
            <li class="toc-level-1"><a href="#approval">8. Approval Information</a></li>
            <li class="toc-level-1"><a href="#case-studies">9. Case Studies</a></li>
            <li class="toc-level-1"><a href="#appendix">10. Appendix</a></li>
        </ul>
    </div>
    
    <!-- Executive Summary -->
    <div class="section" id="executive-summary">
        <h2>1. Executive Summary</h2>
        
        <p>This report presents the findings of the independent validation of the {{ model_metadata.name }} (Model ID: {{ model_metadata.id }}, Version: {{ model_metadata.version }}), which is used by {{ model_metadata.business_unit }} for {{ model_metadata.purpose }}.</p>
        
        <h3>Validation Scope and Approach</h3>
        <p>{{ validation_team.validation_scope }}</p>
        
        <h3>Overall Assessment</h3>
        <p>Based on the comprehensive validation performed, the model has been assigned an overall risk rating of <span class="risk-{{ risk_assessment.overall_risk_rating|lower }}">{{ risk_assessment.overall_risk_rating }}</span>. This assessment reflects the model's statistical performance, conceptual soundness, and implementation quality.</p>
        
        <h3>Key Findings</h3>
        <ul>
            {% for finding in executive_summary.key_findings %}
            <li>{{ finding }}</li>
            {% endfor %}
        </ul>
        
        <h3>Statistical Performance</h3>
        <p>{{ executive_summary.statistical_performance_summary }}</p>
        
        <h3>Model Limitations</h3>
        <ul>
            {% for limitation in model_methodology.limitations %}
            <li>{{ limitation }}</li>
            {% endfor %}
        </ul>
        
        <h3>Key Recommendations</h3>
        <table>
            <thead>
                <tr>
                    <th>Recommendation</th>
                    <th>Owner</th>
                    <th>Timeline</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for recommendation in recommendations.high_priority %}
                <tr>
                    <td>{{ recommendation.description }}</td>
                    <td>{{ recommendation.owner }}</td>
                    <td>{{ recommendation.timeline }}</td>
                    <td>{{ recommendation.status }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <h3>Validation Committee Decision</h3>
        <p>{{ approval_information.decision }} on {{ approval_information.approval_date }}.</p>
        
        {% if approval_information.conditional_requirements %}
        <h4>Conditional Requirements</h4>
        <ol>
            {% for requirement in approval_information.conditional_requirements %}
            <li>{{ requirement }}</li>
            {% endfor %}
        </ol>
        {% endif %}
        
        <p>The next review is scheduled for {{ approval_information.next_review_date }}.</p>
    </div>
    
    <!-- Model Overview -->
    <div class="section" id="model-overview">
        <h2>2. Model Overview</h2>
        
        <div class="subsection" id="model-purpose">
            <h3>2.1 Model Purpose and Use</h3>
            
            <table>
                <tr>
                    <th>Model Name</th>
                    <td>{{ model_metadata.name }}</td>
                </tr>
                <tr>
                    <th>Model ID</th>
                    <td>{{ model_metadata.id }}</td>
                </tr>
                <tr>
                    <th>Version</th>
                    <td>{{ model_metadata.version }}</td>
                </tr>
                <tr>
                    <th>Owner</th>
                    <td>{{ model_metadata.owner }}</td>
                </tr>
                <tr>
                    <th>Developer</th>
                    <td>{{ model_metadata.developer }}</td>
                </tr>
                <tr>
                    <th>Business Unit</th>
                    <td>{{ model_metadata.business_unit }}</td>
                </tr>
                <tr>
                    <th>Purpose</th>
                    <td>{{ model_metadata.purpose }}</td>
                </tr>
                <tr>
                    <th>Implementation Date</th>
                    <td>{{ model_metadata.implementation_date }}</td>
                </tr>
                <tr>
                    <th>Last Validation Date</th>
                    <td>{{ model_metadata.last_validation_date }}</td>
                </tr>
                <tr>
                    <th>Validation Frequency</th>
                    <td>{{ model_metadata.validation_frequency }}</td>
                </tr>
                <tr>
                    <th>Regulatory Framework</th>
                    <td>{{ model_metadata.regulatory_framework }}</td>
                </tr>
            </table>
            
            <p>{{ model_overview.purpose_description }}</p>
        </div>
        
        <div class="subsection" id="model-methodology">
            <h3>2.2 Model Methodology</h3>
            
            <h4>Model Type</h4>
            <p>{{ model_methodology.model_type }}</p>
            
            <h4>Description</h4>
            <p>{{ model_methodology.description }}</p>
            
            <h4>Data Sources</h4>
            <ul>
                {% for source in model_methodology.data_sources %}
                <li>{{ source }}</li>
                {% endfor %}
            </ul>
            
            <h4>Feature Selection</h4>
            <p>{{ model_methodology.feature_selection }}</p>
            
            <h4>Model Training</h4>
            <p>{{ model_methodology.model_training }}</p>
        </div>
        
        <div class="subsection" id="model-assumptions">
            <h3>2.3 Key Assumptions and Limitations</h3>
            
            <h4>Key Assumptions</h4>
            <ul>
                {% for assumption in model_methodology.key_assumptions %}
                <li>{{ assumption }}</li>
                {% endfor %}
            </ul>
            
            <h4>Model Limitations</h4>
            <ul>
                {% for limitation in model_methodology.limitations %}
                <li>{{ limitation }}</li>
                {% endfor %}
            </ul>
            
            <p>{{ model_overview.assumptions_limitations_analysis }}</p>
        </div>
    </div>
    
    <!-- Validation Approach -->
    <div class="section" id="validation-approach">
        <h2>3. Validation Approach</h2>
        
        <p>{{ validation_team.validation_scope }}</p>
        
        <h3>Validation Methodology</h3>
        <p>The validation followed a comprehensive approach that included:</p>
        <ul>
            <li>Statistical performance testing</li>
            <li>Stability analysis</li>
            <li>Sensitivity analysis</li>
            <li>Benchmarking against previous versions and alternatives</li>
            <li>Conceptual soundness assessment</li>
            <li>Implementation review</li>
            <li>Governance evaluation</li>
        </ul>
        
        <h3>Validation Team</h3>
        <p>The validation was conducted by an independent team led by {{ validation_team.lead_validator }}. Team members included:</p>
        <ul>
            {% for member in validation_team.team_members %}
            <li>{{ member }}</li>
            {% endfor %}
        </ul>
        
        <h3>Validation Period</h3>
        <p>{{ validation_team.validation_period }}</p>
    </div>
    
    <!-- Validation Results -->
    <div class="section" id="validation-results">
        <h2>4. Validation Results</h2>
        
        <div class="subsection" id="statistical-performance">
            <h3>4.1 Statistical Performance</h3>
            
            <h4>Performance Metrics</h4>
            <table>
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Training</th>
                        <th>Validation</th>
                        <th>Testing</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>AUC-ROC</td>
                        <td>{{ validation_tests.statistical_performance.auc_roc.training }}</td>
                        <td>{{ validation_tests.statistical_performance.auc_roc.validation }}</td>
                        <td>{{ validation_tests.statistical_performance.auc_roc.testing }}</td>
                    </tr>
                    <tr>
                        <td>Gini Coefficient</td>
                        <td>{{ validation_tests.statistical_performance.gini_coefficient.training }}</td>
                        <td>{{ validation_tests.statistical_performance.gini_coefficient.validation }}</td>
                        <td>{{ validation_tests.statistical_performance.gini_coefficient.testing }}</td>
                    </tr>
                    <tr>
                        <td>KS Statistic</td>
                        <td>{{ validation_tests.statistical_performance.ks_statistic.training }}</td>
                        <td>{{ validation_tests.statistical_performance.ks_statistic.validation }}</td>
                        <td>{{ validation_tests.statistical_performance.ks_statistic.testing }}</td>
                    </tr>
                    <tr>
                        <td>Precision</td>
                        <td>{{ validation_tests.statistical_performance.precision.training }}</td>
                        <td>{{ validation_tests.statistical_performance.precision.validation }}</td>
                        <td>{{ validation_tests.statistical_performance.precision.testing }}</td>
                    </tr>
                    <tr>
                        <td>Recall</td>
                        <td>{{ validation_tests.statistical_performance.recall.training }}</td>
                        <td>{{ validation_tests.statistical_performance.recall.validation }}</td>
                        <td>{{ validation_tests.statistical_performance.recall.testing }}</td>
                    </tr>
                    <tr>
                        <td>F1 Score</td>
                        <td>{{ validation_tests.statistical_performance.f1_score.training }}</td>
                        <td>{{ validation_tests.statistical_performance.f1_score.validation }}</td>
                        <td>{{ validation_tests.statistical_performance.f1_score.testing }}</td>
                    </tr>
                    <tr>
                        <td>Brier Score</td>
                        <td>{{ validation_tests.statistical_performance.brier_score.training }}</td>
                        <td>{{ validation_tests.statistical_performance.brier_score.validation }}</td>
                        <td>{{ validation_tests.statistical_performance.brier_score.testing }}</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="chart">
                <img src="{{ charts.roc_curve }}" alt="ROC Curve">
                <div class="chart-caption">Figure 4.1: ROC Curve</div>
            </div>
            
            <h4>Confusion Matrix</h4>
            <table>
                <tr>
                    <th></th>
                    <th>Predicted Positive</th>
                    <th>Predicted Negative</th>
                </tr>
                <tr>
                    <th>Actual Positive</th>
                    <td>{{ supporting_data.confusion_matrix.true_positives }}</td>
                    <td>{{ supporting_data.confusion_matrix.false_negatives }}</td>
                </tr>
                <tr>
                    <th>Actual Negative</th>
                    <td>{{ supporting_data.confusion_matrix.false_positives }}</td>
                    <td>{{ supporting_data.confusion_matrix.true_negatives }}</td>
                </tr>
            </table>
            
            <p>{{ validation_results.statistical_performance_analysis }}</p>
        </div>
        
        <div class="subsection" id="stability-analysis">
            <h3>4.2 Stability Analysis</h3>
            
            <h4>Population Stability</h4>
            <p>Population Stability Index (PSI): {{ validation_tests.stability_analysis.psi }}</p>
            
            <h4>Characteristic Stability</h4>
            <p>Characteristic Stability Index (CSI) Range: {{ validation_tests.stability_analysis.csi_range }}</p>
            
            <h4>Feature Importance Stability</h4>
            <p>{{ validation_tests.stability_analysis.feature_importance_stability }}</p>
            
            <h4>Temporal Performance</h4>
            <p>{{ validation_tests.stability_analysis.temporal_performance }}</p>
            
            <p>{{ validation_results.stability_analysis }}</p>
        </div>
        
        <div class="subsection" id="sensitivity-analysis">
            <h3>4.3 Sensitivity Analysis</h3>
            
            <h4>Most Sensitive Features</h4>
            <ul>
                {% for feature in validation_tests.sensitivity_analysis.most_sensitive_features %}
                <li>{{ feature.feature }}: {{ feature.impact }}</li>
                {% endfor %}
            </ul>
            
            <h4>Least Sensitive Features</h4>
            <ul>
                {% for feature in validation_tests.sensitivity_analysis.least_sensitive_features %}
                <li>{{ feature.feature }}: {{ feature.impact }}</li>
                {% endfor %}
            </ul>
            
            <h4>Stress Testing Results</h4>
            <p>{{ validation_tests.sensitivity_analysis.stress_testing_results }}</p>
            
            <h4>Extreme Scenario Impact</h4>
            <p>{{ validation_tests.sensitivity_analysis.extreme_scenario_impact }}</p>
            
            <p>{{ validation_results.sensitivity_analysis }}</p>
        </div>
        
        <div class="subsection" id="benchmarking">
            <h3>4.4 Benchmarking</h3>
            
            <h4>Comparison with Previous Version</h4>
            <p>{{ validation_tests.benchmarking.previous_version }}</p>
            
            <h4>Industry Average</h4>
            <p>{{ validation_tests.benchmarking.industry_average }}</p>
            
            <h4>Alternative Models</h4>
            <p>{{ validation_tests.benchmarking.alternative_models }}</p>
            
            <p>{{ validation_results.benchmarking_analysis }}</p>
        </div>
        
        <div class="subsection" id="conceptual-soundness">
            <h3>4.5 Conceptual Soundness</h3>
            
            <h4>Methodology Alignment</h4>
            <p>{{ validation_tests.conceptual_soundness.methodology_alignment }}</p>
            
            <h4>Feature Relationships</h4>
            <p>{{ validation_tests.conceptual_soundness.feature_relationships }}</p>
            
            <h4>Model Complexity</h4>
            <p>{{ validation_tests.conceptual_soundness.model_complexity }}</p>
            
            <h4>Assumptions Validation</h4>
            <p>{{ validation_tests.conceptual_soundness.assumptions_validation }}</p>
            
            <p>{{ validation_results.conceptual_soundness_analysis }}</p>
        </div>
    </div>
    
    <!-- Risk Assessment -->
    <div class="section" id="risk-assessment">
        <h2>5. Risk Assessment</h2>
        
        <h3>Overall Risk Rating</h3>
        <p>Based on the comprehensive validation performed, the model has been assigned an overall risk rating of <span class="risk-{{ risk_assessment.overall_risk_rating|lower }}">{{ risk_assessment.overall_risk_rating }}</span>.</p>
        
        <p>{{ risk_assessment_summary.overall_assessment }}</p>
        
        <h3>Risk Factors</h3>
        
        <table>
            <thead>
                <tr>
                    <th>Risk Factor</th>
                    <th>Rating</th>
                    <th>Findings</th>
                    <th>Issues</th>
                    <th>Mitigation</th>
                </tr>
            </thead>
            <tbody>
                {% for factor in risk_assessment.risk_factors %}
                <tr>
                    <td>{{ factor.name }}</td>
                    <td class="risk-{{ factor.rating|lower }}">{{ factor.rating }}</td>
                    <td>{{ factor.findings }}</td>
                    <td>{{ factor.issues }}</td>
                    <td>{{ factor.mitigation }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <h3>Risk Assessment Methodology</h3>
        <p>{{ risk_assessment_summary.methodology }}</p>
        
        <h3>Risk Rating Criteria</h3>
        <table>
            <thead>
                <tr>
                    <th>Rating</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="risk-high">High</td>
                    <td>{{ risk_assessment_summary.high_risk_criteria }}</td>
                </tr>
                <tr>
                    <td class="risk-medium">Medium</td>
                    <td>{{ risk_assessment_summary.medium_risk_criteria }}</td>
                </tr>
                <tr>
                    <td class="risk-low">Low</td>
                    <td>{{ risk_assessment_summary.low_risk_criteria }}</td>
                </tr>
            </tbody>
        </table>
        
        <h3>Risk Mitigation Strategy</h3>
        <p>{{ risk_assessment_summary.mitigation_strategy }}</p>
        
        <h3>Residual Risk</h3>
        <p>{{ risk_assessment_summary.residual_risk }}</p>
    </div>
    
    <!-- Recommendations -->
    <div class="section" id="recommendations">
        <h2>6. Recommendations</h2>
        
        <p>{{ recommendations_summary.introduction }}</p>
        
        <h3>High Priority Recommendations</h3>
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>Recommendation</th>
                    <th>Owner</th>
                    <th>Timeline</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for recommendation in recommendations.high_priority %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ recommendation.description }}</td>
                    <td>{{ recommendation.owner }}</td>
                    <td>{{ recommendation.timeline }}</td>
                    <td>{{ recommendation.status }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <h3>Medium Priority Recommendations</h3>
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>Recommendation</th>
                    <th>Owner</th>
                    <th>Timeline</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for recommendation in recommendations.medium_priority %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ recommendation.description }}</td>
                    <td>{{ recommendation.owner }}</td>
                    <td>{{ recommendation.timeline }}</td>
                    <td>{{ recommendation.status }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <h3>Low Priority Recommendations</h3>
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>Recommendation</th>
                    <th>Owner</th>
                    <th>Timeline</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for recommendation in recommendations.low_priority %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ recommendation.description }}</td>
                    <td>{{ recommendation.owner }}</td>
                    <td>{{ recommendation.timeline }}</td>
                    <td>{{ recommendation.status }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <h3>Implementation Plan</h3>
        <p>{{ recommendations_summary.implementation_plan }}</p>
        
        <h3>Expected Benefits</h3>
        <p>{{ recommendations_summary.expected_benefits }}</p>
        
        <h3>Monitoring and Follow-up</h3>
        <p>{{ recommendations_summary.monitoring_plan }}</p>
    </div>
    
    <!-- Supporting Data -->
    <div class="section" id="supporting-data">
        <h2>7. Supporting Data</h2>
        
        <div class="subsection" id="performance-metrics-by-segment">
            <h3>7.1 Performance Metrics by Segment</h3>
            
            <table>
                <thead>
                    <tr>
                        <th>Segment</th>
                        <th>AUC-ROC</th>
                        <th>Precision</th>
                        <th>Recall</th>
                    </tr>
                </thead>
                <tbody>
                    {% for segment in supporting_data.performance_metrics_by_segment %}
                    <tr>
                        <td>{{ segment.segment }}</td>
                        <td>{{ segment.auc_roc }}</td>
                        <td>{{ segment.precision }}</td>
                        <td>{{ segment.recall }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <div class="chart">
                <img src="{{ charts.performance_by_segment }}" alt="Performance by Segment">
                <div class="chart-caption">Figure 7.1: Performance Metrics by Segment</div>
            </div>
            
            <p>{{ supporting_data_analysis.segment_performance }}</p>
        </div>
        
        <div class="subsection" id="calibration-analysis">
            <h3>7.2 Calibration Analysis</h3>
            
            <p>Hosmer-Lemeshow test p-value: {{ supporting_data.calibration_analysis.hosmer_lemeshow_p_value }}</p>
            
            <h4>Expected vs. Observed Default Rate by Decile</h4>
            <table>
                <thead>
                    <tr>
                        <th>Decile</th>
                        <th>Expected vs. Observed</th>
                    </tr>
                </thead>
                <tbody>
                    {% for decile in supporting_data.calibration_analysis.decile_analysis %}
                    <tr>
                        <td>{{ decile.decile }}</td>
                        <td>{{ decile.values }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <div class="chart">
                <img src="{{ charts.calibration }}" alt="Calibration Chart">
                <div class="chart-caption">Figure 7.2: Expected vs. Observed Default Rate by Decile</div>
            </div>
            
            <p>{{ supporting_data_analysis.calibration }}</p>
        </div>
        
        <div class="subsection" id="feature-importance">
            <h3>7.3 Feature Importance</h3>
            
            <table>
                <thead>
                    <tr>
                        <th>Feature</th>
                        <th>Importance (%)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for feature in supporting_data.feature_importance %}
                    <tr>
                        <td>{{ feature.feature }}</td>
                        <td>{{ feature.importance }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <div class="chart">
                <img src="{{ charts.feature_importance }}" alt="Feature Importance">
                <div class="chart-caption">Figure 7.3: Feature Importance</div>
            </div>
            
            <p>{{ supporting_data_analysis.feature_importance }}</p>
        </div>
        
        <div class="subsection" id="backtesting-results">
            <h3>7.4 Backtesting Results</h3>
            
            <table>
                <thead>
                    <tr>
                        <th>Period</th>
                        <th>Predicted Default Rate</th>
                        <th>Actual Default Rate</th>
                        <th>Difference</th>
                    </tr>
                </thead>
                <tbody>
                    {% for result in supporting_data.backtesting_results %}
                    <tr>
                        <td>{{ result.period }}</td>
                        <td>{{ result.predicted }}%</td>
                        <td>{{ result.actual }}%</td>
                        <td>{{ (result.predicted - result.actual)|abs }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <div class="chart">
                <img src="{{ charts.backtesting }}" alt="Backtesting Results">
                <div class="chart-caption">Figure 7.4: Predicted vs. Actual Default Rate by Period</div>
            </div>
            
            <p>{{ supporting_data_analysis.backtesting }}</p>
        </div>
        
        <div class="subsection" id="regulatory-compliance">
            <h3>7.5 Regulatory Compliance Checks</h3>
            
            <table>
                <thead>
                    <tr>
                        <th>Compliance Check</th>
                        <th>Result</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>SR 11-7 Compliance</td>
                        <td>{{ supporting_data.regulatory_compliance_checks.sr_11_7_compliance }}</td>
                    </tr>
                    <tr>
                        <td>Fair Lending Analysis</td>
                        <td>{{ supporting_data.regulatory_compliance_checks.fair_lending_analysis }}</td>
                    </tr>
                    <tr>
                        <td>CECL Alignment</td>
                        <td>{{ supporting_data.regulatory_compliance_checks.cecl_alignment }}</td>
                    </tr>
                    <tr>
                        <td>Model Risk Tiering</td>
                        <td>{{ supporting_data.regulatory_compliance_checks.model_risk_tiering }}</td>
                    </tr>
                    <tr>
                        <td>Documentation Completeness</td>
                        <td>{{ supporting_data.regulatory_compliance_checks.documentation_completeness }}</td>
                    </tr>
                </tbody>
            </table>
            
            <p>{{ supporting_data_analysis.regulatory_compliance }}</p>
        </div>
    </div>
    
    <!-- Approval Information -->
    <div class="section" id="approval">
        <h2>8. Approval Information</h2>
        
        <h3>Validation Team</h3>
        <p><strong>Lead Validator:</strong> {{ validation_team.lead_validator }}</p>
        
        <h4>Team Members</h4>
        <ul>
            {% for member in validation_team.team_members %}
            <li>{{ member }}</li>
            {% endfor %}
        </ul>
        
        <h4>Validation Period</h4>
        <p>{{ validation_team.validation_period }}</p>
        
        <h4>Validation Scope</h4>
        <p>{{ validation_team.validation_scope }}</p>
        
        <h3>Validation Committee Decision</h3>
        <p>{{ approval_information.decision }}</p>
        
        <h4>Approval Date</h4>
        <p>{{ approval_information.approval_date }}</p>
        
        {% if approval_information.conditional_requirements %}
        <h4>Conditional Requirements</h4>
        <ol>
            {% for requirement in approval_information.conditional_requirements %}
            <li>{{ requirement }}</li>
            {% endfor %}
        </ol>
        {% endif %}
        
        <h4>Next Review Date</h4>
        <p>{{ approval_information.next_review_date }}</p>
        
        <h3>Signatures</h3>
        <table>
            <tr>
                <th>Role</th>
                <th>Name</th>
                <th>Signature</th>
                <th>Date</th>
            </tr>
            <tr>
                <td>Lead Validator</td>
                <td>{{ validation_team.lead_validator }}</td>
                <td></td>
                <td>{{ approval_information.approval_date }}</td>
            </tr>
            <tr>
                <td>Model Owner</td>
                <td>{{ model_metadata.owner }}</td>
                <td></td>
                <td>{{ approval_information.approval_date }}</td>
            </tr>
            <tr>
                <td>Model Risk Management</td>
                <td>{{ approval_information.mrm_approver }}</td>
                <td></td>
                <td>{{ approval_information.approval_date }}</td>
            </tr>
        </table>
    </div>
    
    <!-- Case Studies -->
    <div class="section" id="case-studies">
        <h2>9. Case Studies</h2>
        
        {% for case_study in case_studies %}
        <div class="subsection" id="case-study-{{ loop.index }}">
            <h3>9.{{ loop.index }} {{ case_study.title }}</h3>
            
            <p><strong>Summary:</strong> {{ case_study.summary }}</p>
            
            <h4>Background</h4>
            <p>{{ case_study.background }}</p>
            
            <h4>Methodology</h4>
            <p>{{ case_study.methodology }}</p>
            
            <h4>Findings</h4>
            <ul>
                {% for finding in case_study.findings %}
                <li>{{ finding }}</li>
                {% endfor %}
            </ul>
            
            <h4>Recommendations</h4>
            <ul>
                {% for recommendation in case_study.recommendations %}
                <li>{{ recommendation }}</li>
                {% endfor %}
            </ul>
            
            <h4>Conclusion</h4>
            <p>{{ case_study.conclusion }}</p>
        </div>
        {% endfor %}
    </div>
    
    <!-- Appendix -->
    <div class="section" id="appendix">
        <h2>10. Appendix</h2>
        
        <div class="subsection" id="glossary">
            <h3>10.1 Glossary</h3>
            
            <table>
                <thead>
                    <tr>
                        <th>Term</th>
                        <th>Definition</th>
                    </tr>
                </thead>
                <tbody>
                    {% for term in appendix.glossary %}
                    <tr>
                        <td>{{ term.term }}</td>
                        <td>{{ term.definition }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="subsection" id="methodology-details">
            <h3>10.2 Detailed Methodology</h3>
            
            <h4>Model Development Process</h4>
            <p>{{ appendix.methodology.development_process }}</p>
            
            <h4>Feature Engineering</h4>
            <p>{{ appendix.methodology.feature_engineering }}</p>
            
            <h4>Model Selection</h4>
            <p>{{ appendix.methodology.model_selection }}</p>
            
            <h4>Hyperparameter Tuning</h4>
            <p>{{ appendix.methodology.hyperparameter_tuning }}</p>
            
            <h4>Training and Validation Approach</h4>
            <p>{{ appendix.methodology.training_validation_approach }}</p>
        </div>
        
        <div class="subsection" id="data-details">
            <h3>10.3 Data Details</h3>
            
            <h4>Data Sources</h4>
            <p>{{ appendix.data.sources }}</p>
            
            <h4>Data Quality Checks</h4>
            <p>{{ appendix.data.quality_checks }}</p>
            
            <h4>Data Preprocessing</h4>
            <p>{{ appendix.data.preprocessing }}</p>
            
            <h4>Feature Distributions</h4>
            <div class="chart">
                <img src="{{ charts.feature_distributions }}" alt="Feature Distributions">
                <div class="chart-caption">Figure 10.1: Feature Distributions</div>
            </div>
        </div>
        
        <div class="subsection" id="validation-details">
            <h3>10.4 Detailed Validation Results</h3>
            
            <h4>Statistical Tests</h4>
            <p>{{ appendix.validation.statistical_tests }}</p>
            
            <h4>Sensitivity Analysis Details</h4>
            <p>{{ appendix.validation.sensitivity_analysis }}</p>
            
            <h4>Stress Testing Scenarios</h4>
            <table>
                <thead>
                    <tr>
                        <th>Scenario</th>
                        <th>Description</th>
                        <th>Impact</th>
                    </tr>
                </thead>
                <tbody>
                    {% for scenario in appendix.validation.stress_testing_scenarios %}
                    <tr>
                        <td>{{ scenario.name }}</td>
                        <td>{{ scenario.description }}</td>
                        <td>{{ scenario.impact }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="subsection" id="regulatory-details">
            <h3>10.5 Regulatory Framework Details</h3>
            
            <h4>SR 11-7 Requirements</h4>
            <p>{{ appendix.regulatory.sr_11_7 }}</p>
            
            <h4>Fair Lending Compliance</h4>
            <p>{{ appendix.regulatory.fair_lending }}</p>
            
            <h4>CECL Requirements</h4>
            <p>{{ appendix.regulatory.cecl }}</p>
        </div>
        
        <div class="subsection" id="references">
            <h3>10.6 References</h3>
            
            <ol>
                {% for reference in appendix.references %}
                <li>{{ reference }}</li>
                {% endfor %}
            </ol>
        </div>
    </div>
    
    <!-- Footer -->
    <div class="footer">
        <p>Confidential - For Internal Use Only</p>
    </div>
</body>
</html>
