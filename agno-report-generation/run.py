#!/usr/bin/env python3
"""
MRM Report Generation System

This script runs the entire MRM Report Generation System, which includes:
1. Parsing the input text file using the Data Parser Agent
2. Generating the HTML and PDF reports using the Report Generator

Usage:
    python run.py --input sample_input.txt --output-dir reports
"""

import argparse
import json
import os
import sys
from typing import Dict, Any

from src.agents.data_parser_agent import DataParserAgent
from src.report_generator.base import ReportGenerator


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="MRM Report Generation System")
    parser.add_argument(
        "--input", "-i", type=str, required=True, help="Path to the input text file"
    )
    parser.add_argument(
        "--output-dir", "-o", type=str, default="reports", help="Directory to save the output files"
    )
    parser.add_argument(
        "--api-key", type=str, help="Anthropic API key (default: from ANTHROPIC_API_KEY env var)"
    )
    parser.add_argument(
        "--save-json", action="store_true", help="Save the parsed data as JSON"
    )
    parser.add_argument(
        "--use-agent", action="store_true", help="Use the Agno agent for parsing (default: use rule-based parser)"
    )
    parser.add_argument(
        "--format", "-f", type=str, choices=["html", "pdf", "docx"], 
        help="Export format (html, pdf, docx). If specified, only generates the report in this format"
    )
    return parser.parse_args()


def main() -> None:
    """Main function."""
    # Parse command line arguments
    args = parse_args()
    
    # Get input file path
    input_file = args.input
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' does not exist")
        sys.exit(1)
    
    # Get output directory
    output_dir = args.output_dir
    os.makedirs(output_dir, exist_ok=True)
    
    # Get API key
    api_key = args.api_key or os.environ.get("ANTHROPIC_API_KEY")
    if not api_key:
        print("Error: Anthropic API key not provided")
        print("Please provide it via --api-key or set the ANTHROPIC_API_KEY environment variable")
        sys.exit(1)
    
    # Get model name from input file
    model_name = os.path.basename(input_file).split('.')[0]
    
    # Set output file paths
    json_file = os.path.join(output_dir, f"{model_name}_parsed_data.json")
    html_file = os.path.join(output_dir, f"{model_name}_report.html")
    pdf_file = os.path.join(output_dir, f"{model_name}_report.pdf")
    
    # Create the Data Parser Agent
    print("Creating Data Parser Agent...")
    data_parser = DataParserAgent(api_key=api_key)
    
    # Parse the input file
    print(f"Parsing input file: {input_file}")
    try:
        if args.use_agent:
            print("Using Agno agent for parsing...")
            parsed_data = data_parser.parse_with_agent(input_file)
        else:
            print("Using rule-based parser...")
            parsed_data = data_parser.parse_file(input_file)
    except Exception as e:
        print(f"Error parsing input file: {str(e)}")
        sys.exit(1)
    
    # Save the parsed data as JSON if requested
    if args.save_json:
        print(f"Saving parsed data to: {json_file}")
        try:
            with open(json_file, "w") as f:
                json.dump(parsed_data.model_dump(), f, indent=2)
        except Exception as e:
            print(f"Error saving parsed data: {str(e)}")
            sys.exit(1)
    
    # Create the Report Generator
    print("Creating Report Generator...")
    report_generator = ReportGenerator(template_dir="templates")
    
    # Generate the reports
    if args.format:
        # Export report in the specified format
        output_file = os.path.join(output_dir, f"{model_name}_report.{args.format}")
        print(f"Generating {args.format.upper()} report: {output_file}")
        try:
            report_generator.export_report(
                parsed_data=parsed_data.model_dump(),
                output_format=args.format,
                output_path=output_file
            )
            print(f"{args.format.upper()} report: {output_file}")
        except NotImplementedError as e:
            print(f"Error: {str(e)}")
            sys.exit(1)
        except Exception as e:
            print(f"Error generating report: {str(e)}")
            sys.exit(1)
    else:
        # Generate both HTML and PDF reports
        print(f"Generating reports: {html_file} and {pdf_file}")
        try:
            report_generator.generate_report(
                parsed_data=parsed_data.model_dump(),
                output_html=html_file,
                output_pdf=pdf_file
            )
            print(f"HTML report: {html_file}")
            print(f"PDF report: {pdf_file}")
        except Exception as e:
            print(f"Error generating reports: {str(e)}")
            sys.exit(1)
    
    print("Done!")


if __name__ == "__main__":
    main()
