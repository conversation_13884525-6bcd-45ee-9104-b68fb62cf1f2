# Model Risk Management (MRM) Report Generation System

A system that generates comprehensive Model Risk Management Final Validation reports in PDF format using the Agno framework and a multi-agent workflow.

## Overview

The MRM Report Generation System transforms raw model validation data from text files into a professionally formatted 200+ page PDF report through a coordinated workflow of specialized AI agents, HTML templates, and PDF generation tools.

```
┌─────────────┐     ┌──────────────┐     ┌────────────────┐     ┌─────────────┐
│  Input Data │────▶│ Agent System │────▶│ HTML Templates │────▶│ Final PDF   │
│  (txt file) │     │ (Processing) │     │ (Formatting)   │     │ (Output)    │
└─────────────┘     └──────────────┘     └────────────────┘     └─────────────┘
```

## Features

- **Data Parsing**: Extract structured information from raw text files
- **Content Enhancement**: Expand technical descriptions with proper context
- **Executive Summary Generation**: Create management-friendly summaries
- **Visualization**: Generate advanced charts and graphs from metrics
  - Dual-panel backtesting charts showing predicted vs. actual values
  - Feature importance visualizations
  - Performance by segment analysis
  - ROC curves and calibration plots
- **Code Snippets**: Include properly formatted code examples with syntax highlighting
- **Sensitivity Analysis**: Comprehensive sensitivity testing with detailed metrics
- **Compliance Checking**: Ensure regulatory compliance
- **Quality Assurance**: Review for accuracy and consistency
- **PDF Generation**: Create professional PDF reports with proper formatting

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd mrm_report_system
```

2. Create and activate a virtual environment:

```bash
# Create a virtual environment
python -m venv venv

# Activate on macOS/Linux
source venv/bin/activate

# Activate on Windows
# venv\Scripts\activate
```

3. Install the required dependencies:

```bash
pip install -r requirements.txt
```

4. Set up your Anthropic API key:

```bash
# On macOS/Linux
export ANTHROPIC_API_KEY=your-api-key

# On Windows
# set ANTHROPIC_API_KEY=your-api-key
```

## Usage

### Running the System

To generate a complete MRM report:

```bash
# Basic usage
./run.py --input sample_input.txt

# Save parsed data as JSON
./run.py --input sample_input.txt --save-json

# Specify output directory
./run.py --input sample_input.txt --output-dir custom_reports

# Use Agno agent for parsing (instead of rule-based parser)
./run.py --input sample_input.txt --use-agent

# Provide API key directly
./run.py --input sample_input.txt --api-key your-api-key
```

### Input File Format

The input file should be a text file with clearly demarcated sections. Each section should start with a header in the format `===== SECTION_NAME =====`. See `sample_input.txt` for an example.

The following sections are expected:

- `MODEL METADATA`: Basic information about the model
- `MODEL METHODOLOGY`: Details about the model's methodology
- `VALIDATION TESTS AND RESULTS`: Results of validation tests
- `RISK ASSESSMENT FINDINGS`: Risk assessment findings
- `RECOMMENDATIONS`: Recommendations for improvement
- `SUPPORTING DATA`: Supporting data and metrics
- `VALIDATION TEAM`: Information about the validation team
- `APPROVAL INFORMATION`: Approval information

### Output Files

The system generates the following output files:

- `<model_name>_report.html`: HTML version of the report
- `<model_name>_report.pdf`: PDF version of the report
- `<model_name>_parsed_data.json`: JSON file with parsed data (if `--save-json` is specified)

## Project Structure

```
mrm_report_system/
├── README.md                      # Project documentation
├── requirements.txt               # Dependencies
├── run.py                         # Main entry point
├── sample_input.txt               # Sample input data
├── src/
│   ├── __init__.py
│   ├── main.py                    # Command-line interface
│   ├── report_generator.py        # Report generation (backward compatibility)
│   ├── report_generator/          # Modular report generator
│   │   ├── __init__.py
│   │   ├── base.py                # Main ReportGenerator class
│   │   ├── charts.py              # Chart generation
│   │   ├── content_generators.py  # Text content generation
│   │   ├── case_studies.py        # Case studies generation
│   │   ├── appendices.py          # Appendices generation
│   │   ├── additional_content.py  # Additional content generation
│   │   ├── technical_documentation.py  # Technical documentation generation
│   │   ├── regulatory_details.py  # Regulatory details generation
│   │   ├── model_history.py       # Model history generation
│   │   ├── data_quality.py        # Data quality assessment generation
│   │   └── implementation_guidelines.py  # Implementation guidelines generation
│   ├── agents/
│   │   ├── __init__.py
│   │   ├── data_parser_agent.py           # Data Parser Agent
│   │   ├── content_enhancement_agent.py   # Content Enhancement Agent
│   │   ├── executive_summary_agent.py     # Executive Summary Agent
│   │   ├── visualization_agent.py         # Visualization Agent
│   │   ├── compliance_agent.py            # Compliance Agent
│   │   ├── quality_assurance_agent.py     # Quality Assurance Agent
│   │   └── coordinator_agent.py           # Coordinator Agent
│   └── models/
│       ├── __init__.py
│       └── data_models.py         # Data structure models
└── templates/                     # HTML templates
    ├── base.html                  # Base template
    ├── report.html                # Main report template
    ├── executive_summary.html     # Executive summary template
    ├── model_overview.html        # Model overview template
    ├── validation_results.html    # Validation results template
    ├── risk_assessment.html       # Risk assessment template
    ├── recommendations.html       # Recommendations template
    ├── supporting_data.html       # Supporting data template
    ├── approval.html              # Approval information template
    └── appendix.html              # Appendix template
```

## Development Status

This project is under active development. Currently implemented:

- [x] Data models
- [x] Data Parser Agent
- [x] HTML templates
- [x] Report Generator (modular architecture)
- [x] PDF generation
- [x] Case Studies generation
- [x] Appendices generation
- [x] Detailed content generation
- [x] Content Enhancement Agent
- [x] Executive Summary Agent
- [x] Visualization Agent
  - [x] Advanced backtesting charts with dual-panel layout
  - [x] Feature importance visualizations
  - [x] Performance by segment analysis
  - [x] ROC curves and calibration plots
- [x] Compliance Agent
- [x] Quality Assurance Agent
- [x] Coordinator Agent
- [x] Code snippet formatting with syntax highlighting
- [x] Comprehensive sensitivity analysis
- [x] Detailed recommendations with implementation plans

## Roadmap

Future development plans include:

1. **Q3 2025**: Optimize agent performance and improve error handling
2. **Q4 2025**: Enhance visualization capabilities and add more chart types
3. **Q1 2026**: Implement support for additional model types and regulatory frameworks
4. **Q2 2026**: Add multi-language support and customizable branding options

## Recent Enhancements

### Advanced Visualizations

The system now includes enhanced visualization capabilities:

- **Dual-Panel Backtesting Charts**: Shows predicted vs. actual values in the top panel and the difference between them in the bottom panel, with color-coded bars to indicate over-prediction vs. under-prediction.
- **Feature Importance Visualizations**: Horizontal bar charts showing the relative importance of each feature in the model.
- **Performance by Segment Analysis**: Bar charts comparing model performance across different customer segments.
- **ROC Curves and Calibration Plots**: Visual representations of model discrimination and calibration.

### Code Snippet Integration

The system now supports embedding properly formatted code snippets in the report:

- **Syntax Highlighting**: Code is displayed with proper syntax highlighting for better readability.
- **Implementation Examples**: Includes example code for key model components like sensitivity testing and backtesting.
- **Formatted Output**: Code blocks are styled with appropriate fonts, colors, and spacing.

### Comprehensive Sensitivity Analysis

Enhanced sensitivity analysis capabilities:

- **Feature Elasticity Metrics**: Detailed measurements of how changes in input features affect model outputs.
- **Stress Testing Results**: Tabular presentation of model performance under various economic scenarios.
- **Extreme Value Analysis**: Assessment of model behavior with extreme input values.

### Detailed Recommendations

Improved recommendations section:

- **Prioritized Recommendations**: Recommendations are categorized as high, medium, or low priority.
- **Implementation Plans**: Each recommendation includes an owner, timeline, and status.
- **Expected Benefits**: Clear explanation of the benefits of implementing each recommendation.

## Extending the System

### Adding New Agents

To add a new agent:

1. Create a new file in the `src/agents` directory
2. Implement the agent using the Agno framework
3. Update the `run.py` script to use the new agent

### Customizing Templates

To customize the templates:

1. Edit the HTML templates in the `templates` directory
2. Update the CSS styles in the `templates/base.html` file

### Adding New Visualizations

To add new chart types:

1. Add a new chart generation function in `src/report_generator/charts.py`
2. Update the `generate_charts` function to include the new chart
3. Modify the relevant template to display the new chart

## License

This project is licensed under the MIT License - see the LICENSE file for details.
