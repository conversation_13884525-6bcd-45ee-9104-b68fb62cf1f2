#!/bin/bash
# Test script for the MRM Report Generation System
# This script runs the system with the sample input file and saves the output to the reports directory

# Create reports directory if it doesn't exist
mkdir -p reports

# Set API key (replace with your actual API key)
export ANTHROPIC_API_KEY="************************************************************************************************************"

# Run the system with the sample input file to generate HTML and PDF reports
echo "Running the MRM Report Generation System with sample_input.txt..."
./run.py --input sample_input.txt --save-json

# Check if the reports were generated
if [ -f "reports/sample_input_report.html" ] && [ -f "reports/sample_input_report.pdf" ]; then
    echo "Success! Reports generated successfully."
    echo "HTML report: reports/sample_input_report.html"
    echo "PDF report: reports/sample_input_report.pdf"
    echo "JSON data: reports/sample_input_parsed_data.json"
else
    echo "Error: Reports were not generated."
    exit 1
fi

# Test the HTML export format
echo "Testing HTML export format..."
./run.py --input sample_input.txt --format html --output-dir reports/formats

# Check if the HTML report was generated
if [ -f "reports/formats/sample_input_report.html" ]; then
    echo "Success! HTML report generated successfully."
    echo "HTML report: reports/formats/sample_input_report.html"
else
    echo "Error: HTML report was not generated."
    exit 1
fi

# Test the PDF export format
echo "Testing PDF export format..."
./run.py --input sample_input.txt --format pdf --output-dir reports/formats

# Check if the PDF report was generated
if [ -f "reports/formats/sample_input_report.pdf" ]; then
    echo "Success! PDF report generated successfully."
    echo "PDF report: reports/formats/sample_input_report.pdf"
else
    echo "Error: PDF report was not generated."
    exit 1
fi

echo "Done!"
