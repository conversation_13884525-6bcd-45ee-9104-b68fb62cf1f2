"""
Data models for the MRM Report Generation System.

This module defines the Pydantic models that represent the structured data
extracted from the input file and used throughout the report generation process.
"""

from typing import Dict, List, Optional
from pydantic import BaseModel, Field


class ModelMetadata(BaseModel):
    """Model metadata information."""
    name: str = Field(..., description="Name of the model")
    id: str = Field(..., description="Unique identifier for the model")
    version: str = Field(..., description="Version of the model")
    owner: str = Field(..., description="Department or team that owns the model")
    developer: str = Field(..., description="Team that developed the model")
    business_unit: str = Field(..., description="Business unit using the model")
    purpose: str = Field(..., description="Purpose of the model")
    implementation_date: str = Field(..., description="Date the model was implemented")
    last_validation_date: str = Field(..., description="Date of the last validation")
    validation_frequency: str = Field(..., description="How often the model is validated")
    regulatory_framework: str = Field(..., description="Regulatory frameworks applicable to the model")


class ModelMethodology(BaseModel):
    """Model methodology information."""
    model_type: str = Field(..., description="Type of model")
    description: str = Field(..., description="Detailed description of the model")
    key_assumptions: List[str] = Field(..., description="Key assumptions of the model")
    limitations: List[str] = Field(..., description="Limitations of the model")
    data_sources: List[str] = Field(..., description="Data sources used by the model")
    feature_selection: str = Field(..., description="Feature selection process")
    model_training: str = Field(..., description="Model training process")


class StatisticalPerformance(BaseModel):
    """Statistical performance metrics."""
    auc_roc: Dict[str, float] = Field(..., description="AUC-ROC scores")
    gini_coefficient: Dict[str, float] = Field(..., description="Gini coefficient scores")
    ks_statistic: Dict[str, float] = Field(..., description="KS statistic scores")
    precision: Dict[str, float] = Field(..., description="Precision scores")
    recall: Dict[str, float] = Field(..., description="Recall scores")
    f1_score: Dict[str, float] = Field(..., description="F1 scores")
    brier_score: Dict[str, float] = Field(..., description="Brier scores")


class StabilityAnalysis(BaseModel):
    """Stability analysis results."""
    psi: float = Field(..., description="Population Stability Index")
    csi_range: str = Field(..., description="Range of Characteristic Stability Index across features")
    feature_importance_stability: str = Field(..., description="Stability of feature importance")
    temporal_performance: str = Field(..., description="Temporal performance variation")


class SensitivityAnalysis(BaseModel):
    """Sensitivity analysis results."""
    most_sensitive_features: List[Dict[str, str]] = Field(..., description="Most sensitive features")
    least_sensitive_features: List[Dict[str, str]] = Field(..., description="Least sensitive features")
    stress_testing_results: str = Field(..., description="Stress testing results")
    extreme_scenario_impact: str = Field(..., description="Impact of extreme scenarios")


class Benchmarking(BaseModel):
    """Benchmarking results."""
    previous_version: str = Field(..., description="Comparison with previous model version")
    industry_average: str = Field(..., description="Comparison with industry average")
    alternative_models: str = Field(..., description="Comparison with alternative models")


class ConceptualSoundness(BaseModel):
    """Conceptual soundness assessment."""
    methodology_alignment: str = Field(..., description="Alignment of methodology with best practices")
    feature_relationships: str = Field(..., description="Consistency of feature relationships")
    model_complexity: str = Field(..., description="Appropriateness of model complexity")
    assumptions_validation: str = Field(..., description="Validation of assumptions")


class ValidationTests(BaseModel):
    """Validation tests and results."""
    statistical_performance: StatisticalPerformance
    stability_analysis: StabilityAnalysis
    sensitivity_analysis: SensitivityAnalysis
    benchmarking: Benchmarking
    conceptual_soundness: ConceptualSoundness


class RiskFactor(BaseModel):
    """Risk factor assessment."""
    name: str = Field(..., description="Name of the risk factor")
    rating: str = Field(..., description="Risk rating (Low, Medium, High)")
    findings: str = Field(..., description="Findings related to the risk factor")
    issues: str = Field(..., description="Issues identified")
    mitigation: str = Field(..., description="Mitigation measures")


class RiskAssessment(BaseModel):
    """Risk assessment findings."""
    overall_risk_rating: str = Field(..., description="Overall risk rating")
    risk_factors: List[RiskFactor] = Field(..., description="Assessment of individual risk factors")


class Recommendation(BaseModel):
    """Recommendation details."""
    description: str = Field(..., description="Description of the recommendation")
    owner: str = Field(..., description="Team or person responsible for implementation")
    timeline: str = Field(..., description="Timeline for implementation")
    status: str = Field(..., description="Current status")


class Recommendations(BaseModel):
    """Recommendations section."""
    high_priority: List[Recommendation] = Field(..., description="High priority recommendations")
    medium_priority: List[Recommendation] = Field(..., description="Medium priority recommendations")
    low_priority: List[Recommendation] = Field(..., description="Low priority recommendations")


class PerformanceMetricsBySegment(BaseModel):
    """Performance metrics by segment."""
    segment: str = Field(..., description="Segment name")
    auc_roc: float = Field(..., description="AUC-ROC for the segment")
    precision: float = Field(..., description="Precision for the segment")
    recall: float = Field(..., description="Recall for the segment")


class CalibrationAnalysis(BaseModel):
    """Calibration analysis results."""
    hosmer_lemeshow_p_value: float = Field(..., description="Hosmer-Lemeshow test p-value")
    decile_analysis: List[Dict[str, str]] = Field(..., description="Expected vs. observed by decile")


class ConfusionMatrix(BaseModel):
    """Confusion matrix."""
    true_positives: int = Field(..., description="True positives")
    false_positives: int = Field(..., description="False positives")
    true_negatives: int = Field(..., description="True negatives")
    false_negatives: int = Field(..., description="False negatives")


class ROCCurve(BaseModel):
    """ROC curve coordinates."""
    coordinates: List[List[float]] = Field(..., description="ROC curve coordinates")


class FeatureImportance(BaseModel):
    """Feature importance."""
    feature: str = Field(..., description="Feature name")
    importance: float = Field(..., description="Importance percentage")


class BacktestingResults(BaseModel):
    """Backtesting results."""
    period: str = Field(..., description="Time period")
    predicted: float = Field(..., description="Predicted default rate")
    actual: float = Field(..., description="Actual default rate")


class RegulatoryComplianceChecks(BaseModel):
    """Regulatory compliance checks."""
    sr_11_7_compliance: str = Field(..., description="SR 11-7 compliance status")
    fair_lending_analysis: str = Field(..., description="Fair lending analysis results")
    cecl_alignment: str = Field(..., description="CECL alignment status")
    model_risk_tiering: str = Field(..., description="Model risk tiering")
    documentation_completeness: str = Field(..., description="Documentation completeness percentage")


class SupportingData(BaseModel):
    """Supporting data section."""
    performance_metrics_by_segment: List[PerformanceMetricsBySegment]
    calibration_analysis: CalibrationAnalysis
    confusion_matrix: ConfusionMatrix
    roc_curve: ROCCurve
    feature_importance: List[FeatureImportance]
    backtesting_results: List[BacktestingResults]
    regulatory_compliance_checks: RegulatoryComplianceChecks


class ValidationTeam(BaseModel):
    """Validation team information."""
    lead_validator: str = Field(..., description="Lead validator name and title")
    team_members: List[str] = Field(..., description="Team members with titles")
    validation_period: str = Field(..., description="Period during which validation was conducted")
    validation_scope: str = Field(..., description="Scope of the validation")


class ApprovalInformation(BaseModel):
    """Approval information."""
    decision: str = Field(..., description="Validation committee decision")
    approval_date: str = Field(..., description="Date of approval")
    conditional_requirements: List[str] = Field(..., description="Conditional requirements if any")
    next_review_date: str = Field(..., description="Date of next review")


class ParsedData(BaseModel):
    """Complete parsed data from the input file."""
    model_metadata: ModelMetadata
    model_methodology: ModelMethodology
    validation_tests: ValidationTests
    risk_assessment: RiskAssessment
    recommendations: Recommendations
    supporting_data: SupportingData
    validation_team: ValidationTeam
    approval_information: ApprovalInformation
