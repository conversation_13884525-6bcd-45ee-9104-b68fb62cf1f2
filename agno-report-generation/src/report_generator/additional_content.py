"""
Additional content generation module for the MRM Report Generation System.

This module contains methods for generating additional content for the report.
"""

from typing import Dict, Any

from .technical_documentation import generate_technical_documentation
from .regulatory_details import generate_regulatory_details
from .model_history import generate_model_history
from .data_quality import generate_data_quality_assessment
from .implementation_guidelines import generate_implementation_guidelines


# Re-export the functions for backward compatibility
__all__ = [
    "generate_technical_documentation",
    "generate_regulatory_details",
    "generate_model_history",
    "generate_data_quality_assessment",
    "generate_implementation_guidelines"
]
