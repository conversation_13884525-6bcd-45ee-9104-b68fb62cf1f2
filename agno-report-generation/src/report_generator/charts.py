"""
Chart generation module for the MRM Report Generation System.

This module contains methods for generating charts for the report.
"""

import os
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, Any, List


def generate_charts(parsed_data: Dict[str, Any], charts_dir: str) -> Dict[str, str]:
    """
    Generate charts for the report.

    Args:
        parsed_data: Parsed data from the input file
        charts_dir: Directory to save the charts

    Returns:
        Dict[str, str]: Dictionary mapping chart names to file paths
    """
    charts = {}
    
    # Generate ROC curve
    charts["roc_curve"] = generate_roc_curve(parsed_data, charts_dir)
    
    # Generate performance by segment chart
    charts["performance_by_segment"] = generate_performance_by_segment_chart(parsed_data, charts_dir)
    
    # Generate calibration chart
    charts["calibration"] = generate_calibration_chart(parsed_data, charts_dir)
    
    # Generate feature importance chart
    charts["feature_importance"] = generate_feature_importance_chart(parsed_data, charts_dir)
    
    # Generate backtesting chart
    charts["backtesting"] = generate_backtesting_chart(parsed_data, charts_dir)
    
    # Generate feature distributions chart
    charts["feature_distributions"] = generate_feature_distributions_chart(parsed_data, charts_dir)
    
    return charts


def generate_roc_curve(parsed_data: Dict[str, Any], charts_dir: str) -> str:
    """
    Generate ROC curve chart.

    Args:
        parsed_data: Parsed data from the input file
        charts_dir: Directory to save the chart

    Returns:
        str: Path to the generated chart
    """
    # Extract ROC curve coordinates
    coordinates = parsed_data["supporting_data"]["roc_curve"]["coordinates"]
    
    # Create the chart
    plt.figure(figsize=(8, 6))
    
    # Plot the ROC curve
    fpr = [coord[0] for coord in coordinates]
    tpr = [coord[1] for coord in coordinates]
    plt.plot(fpr, tpr, label=f"AUC = {parsed_data['validation_tests']['statistical_performance']['auc_roc']['testing']:.2f}")
    
    # Plot the diagonal line
    plt.plot([0, 1], [0, 1], linestyle="--", color="gray")
    
    # Set labels and title
    plt.xlabel("False Positive Rate")
    plt.ylabel("True Positive Rate")
    plt.title("ROC Curve")
    
    # Add legend
    plt.legend()
    
    # Add grid
    plt.grid(True, linestyle="--", alpha=0.7)
    
    # Set limits
    plt.xlim([0, 1])
    plt.ylim([0, 1])
    
    # Save the chart
    chart_path = os.path.join(charts_dir, "roc_curve.png")
    plt.savefig(chart_path, dpi=300, bbox_inches="tight")
    plt.close()
    
    return chart_path


def generate_performance_by_segment_chart(parsed_data: Dict[str, Any], charts_dir: str) -> str:
    """
    Generate performance by segment chart.

    Args:
        parsed_data: Parsed data from the input file
        charts_dir: Directory to save the chart

    Returns:
        str: Path to the generated chart
    """
    # Extract performance metrics by segment
    segments = parsed_data["supporting_data"]["performance_metrics_by_segment"]
    
    # Create the chart
    plt.figure(figsize=(10, 6))
    
    # Set up the bar chart
    segment_names = [segment["segment"] for segment in segments]
    auc_roc = [segment["auc_roc"] for segment in segments]
    precision = [segment["precision"] for segment in segments]
    recall = [segment["recall"] for segment in segments]
    
    # Set up the bar positions
    x = np.arange(len(segment_names))
    width = 0.25
    
    # Create the bars
    plt.bar(x - width, auc_roc, width, label="AUC-ROC")
    plt.bar(x, precision, width, label="Precision")
    plt.bar(x + width, recall, width, label="Recall")
    
    # Set labels and title
    plt.xlabel("Segment")
    plt.ylabel("Score")
    plt.title("Performance Metrics by Segment")
    
    # Set x-axis ticks
    plt.xticks(x, segment_names, rotation=45, ha="right")
    
    # Add legend
    plt.legend()
    
    # Add grid
    plt.grid(True, linestyle="--", alpha=0.7, axis="y")
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the chart
    chart_path = os.path.join(charts_dir, "performance_by_segment.png")
    plt.savefig(chart_path, dpi=300, bbox_inches="tight")
    plt.close()
    
    return chart_path


def generate_calibration_chart(parsed_data: Dict[str, Any], charts_dir: str) -> str:
    """
    Generate calibration chart.

    Args:
        parsed_data: Parsed data from the input file
        charts_dir: Directory to save the chart

    Returns:
        str: Path to the generated chart
    """
    # Extract calibration analysis
    decile_analysis = parsed_data["supporting_data"]["calibration_analysis"]["decile_analysis"]
    
    # Create the chart
    plt.figure(figsize=(8, 6))
    
    # Extract expected and observed values
    deciles = []
    expected = []
    observed = []
    
    for decile in decile_analysis:
        decile_name = decile["decile"].split(" ")[1]
        deciles.append(int(decile_name))
        
        values = decile["values"].split("vs.")
        expected.append(float(values[0].strip().rstrip("%")))
        observed.append(float(values[1].strip().rstrip("%")))
    
    # Plot the expected and observed values
    plt.plot(deciles, expected, marker="o", label="Expected")
    plt.plot(deciles, observed, marker="x", label="Observed")
    
    # Set labels and title
    plt.xlabel("Decile")
    plt.ylabel("Default Rate (%)")
    plt.title("Expected vs. Observed Default Rate by Decile")
    
    # Add legend
    plt.legend()
    
    # Add grid
    plt.grid(True, linestyle="--", alpha=0.7)
    
    # Set x-axis ticks
    plt.xticks(deciles)
    
    # Save the chart
    chart_path = os.path.join(charts_dir, "calibration.png")
    plt.savefig(chart_path, dpi=300, bbox_inches="tight")
    plt.close()
    
    return chart_path


def generate_feature_importance_chart(parsed_data: Dict[str, Any], charts_dir: str) -> str:
    """
    Generate feature importance chart.

    Args:
        parsed_data: Parsed data from the input file
        charts_dir: Directory to save the chart

    Returns:
        str: Path to the generated chart
    """
    # Extract feature importance
    feature_importance = parsed_data["supporting_data"]["feature_importance"]
    
    # Create the chart
    plt.figure(figsize=(10, 6))
    
    # Extract feature names and importance values
    features = [feature["feature"] for feature in feature_importance]
    importance = [feature["importance"] for feature in feature_importance]
    
    # Sort by importance
    sorted_indices = np.argsort(importance)
    features = [features[i] for i in sorted_indices]
    importance = [importance[i] for i in sorted_indices]
    
    # Create the horizontal bar chart
    plt.barh(features, importance)
    
    # Set labels and title
    plt.xlabel("Importance (%)")
    plt.ylabel("Feature")
    plt.title("Feature Importance")
    
    # Add grid
    plt.grid(True, linestyle="--", alpha=0.7, axis="x")
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the chart
    chart_path = os.path.join(charts_dir, "feature_importance.png")
    plt.savefig(chart_path, dpi=300, bbox_inches="tight")
    plt.close()
    
    return chart_path


def generate_backtesting_chart(parsed_data: Dict[str, Any], charts_dir: str) -> str:
    """
    Generate backtesting chart.

    Args:
        parsed_data: Parsed data from the input file
        charts_dir: Directory to save the chart

    Returns:
        str: Path to the generated chart
    """
    # Extract backtesting results
    backtesting_results = parsed_data["supporting_data"]["backtesting_results"]
    
    # Create the chart with a larger figure size for better visibility
    plt.figure(figsize=(12, 8))
    
    # Extract periods, predicted, and actual values
    periods = [result["period"] for result in backtesting_results]
    predicted = [result["predicted"] for result in backtesting_results]
    actual = [result["actual"] for result in backtesting_results]
    
    # Calculate the difference between predicted and actual
    difference = [pred - act for pred, act in zip(predicted, actual)]
    
    # Create a subplot grid: 2 rows, 1 column
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]})
    
    # Plot the predicted and actual values on the first subplot
    ax1.plot(periods, predicted, marker="o", linewidth=2, color='#1f77b4', label="Predicted")
    ax1.plot(periods, actual, marker="x", linewidth=2, color='#d62728', label="Actual")
    
    # Fill the area between the lines
    ax1.fill_between(periods, predicted, actual, alpha=0.2, color='gray')
    
    # Add data labels
    for i, (p, a) in enumerate(zip(predicted, actual)):
        ax1.annotate(f"{p}%", (periods[i], p), textcoords="offset points", 
                    xytext=(0,10), ha='center', fontweight='bold')
        ax1.annotate(f"{a}%", (periods[i], a), textcoords="offset points", 
                    xytext=(0,-15), ha='center', fontweight='bold')
    
    # Set labels and title for the first subplot
    ax1.set_xlabel("Period", fontsize=12)
    ax1.set_ylabel("Default Rate (%)", fontsize=12)
    ax1.set_title("Predicted vs. Actual Default Rate by Period", fontsize=14, fontweight='bold')
    
    # Add legend to the first subplot
    ax1.legend(fontsize=12)
    
    # Add grid to the first subplot
    ax1.grid(True, linestyle="--", alpha=0.7)
    
    # Plot the difference on the second subplot
    bars = ax2.bar(periods, difference, color=['#d62728' if d < 0 else '#1f77b4' for d in difference])
    
    # Add data labels to the bars
    for bar, diff in zip(bars, difference):
        height = bar.get_height()
        ax2.annotate(f"{diff:.2f}%",
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3 if diff >= 0 else -14),  # 3 points vertical offset
                    textcoords="offset points",
                    ha='center', va='bottom' if diff >= 0 else 'top',
                    fontweight='bold')
    
    # Set labels and title for the second subplot
    ax2.set_xlabel("Period", fontsize=12)
    ax2.set_ylabel("Difference (%)", fontsize=12)
    ax2.set_title("Difference Between Predicted and Actual (Predicted - Actual)", fontsize=12)
    
    # Add a horizontal line at y=0 for the second subplot
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # Add grid to the second subplot
    ax2.grid(True, linestyle="--", alpha=0.7, axis='y')
    
    # Rotate x-axis labels
    ax1.set_xticklabels(periods, rotation=45, ha="right")
    ax2.set_xticklabels(periods, rotation=45, ha="right")
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the chart
    chart_path = os.path.join(charts_dir, "backtesting.png")
    plt.savefig(chart_path, dpi=300, bbox_inches="tight")
    plt.close()
    
    return chart_path


def generate_feature_distributions_chart(parsed_data: Dict[str, Any], charts_dir: str) -> str:
    """
    Generate feature distributions chart.

    Args:
        parsed_data: Parsed data from the input file
        charts_dir: Directory to save the chart

    Returns:
        str: Path to the generated chart
    """
    # Create a sample feature distributions chart
    plt.figure(figsize=(12, 8))
    
    # Create a 2x2 grid of subplots
    fig, axs = plt.subplots(2, 2, figsize=(12, 8))
    
    # Sample data for the distributions
    np.random.seed(42)
    
    # Credit score distribution
    credit_scores = np.random.normal(700, 100, 1000)
    credit_scores = np.clip(credit_scores, 300, 850)
    axs[0, 0].hist(credit_scores, bins=20, alpha=0.7)
    axs[0, 0].set_title("Credit Score Distribution")
    axs[0, 0].set_xlabel("Credit Score")
    axs[0, 0].set_ylabel("Frequency")
    
    # Debt-to-income ratio distribution
    dti_ratios = np.random.gamma(2, 0.1, 1000)
    dti_ratios = np.clip(dti_ratios, 0, 1)
    axs[0, 1].hist(dti_ratios, bins=20, alpha=0.7)
    axs[0, 1].set_title("Debt-to-Income Ratio Distribution")
    axs[0, 1].set_xlabel("Debt-to-Income Ratio")
    axs[0, 1].set_ylabel("Frequency")
    
    # Loan amount distribution
    loan_amounts = np.random.lognormal(10, 1, 1000)
    axs[1, 0].hist(loan_amounts, bins=20, alpha=0.7)
    axs[1, 0].set_title("Loan Amount Distribution")
    axs[1, 0].set_xlabel("Loan Amount")
    axs[1, 0].set_ylabel("Frequency")
    
    # Employment stability distribution
    employment_stability = np.random.exponential(5, 1000)
    employment_stability = np.clip(employment_stability, 0, 30)
    axs[1, 1].hist(employment_stability, bins=20, alpha=0.7)
    axs[1, 1].set_title("Employment Stability Distribution")
    axs[1, 1].set_xlabel("Years")
    axs[1, 1].set_ylabel("Frequency")
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the chart
    chart_path = os.path.join(charts_dir, "feature_distributions.png")
    plt.savefig(chart_path, dpi=300, bbox_inches="tight")
    plt.close()
    
    return chart_path
