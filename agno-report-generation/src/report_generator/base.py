"""
Base module for the MRM Report Generation System.

This module contains the main ReportGenerator class that is responsible for generating
the HTML report from the parsed data and converting it to a PDF file.
"""

import os
import markdown
from typing import Dict, Any

from jinja2 import Environment, FileSystemLoader
from weasyprint import HTML

from .charts import generate_charts
from .case_studies import generate_case_studies
from .appendices import generate_detailed_appendices
from .content_generators import (
    generate_key_findings,
    generate_statistical_performance_summary,
    generate_purpose_description,
    generate_assumptions_limitations_analysis,
    generate_statistical_performance_analysis,
    generate_stability_analysis,
    generate_sensitivity_analysis,
    generate_benchmarking_analysis,
    generate_conceptual_soundness_analysis,
    generate_risk_assessment_overall,
    generate_risk_assessment_methodology,
    generate_risk_mitigation_strategy,
    generate_residual_risk,
    generate_recommendations_introduction,
    generate_implementation_plan,
    generate_expected_benefits,
    generate_monitoring_plan,
    generate_segment_performance_analysis,
    generate_calibration_analysis,
    generate_feature_importance_analysis,
    generate_backtesting_analysis,
    generate_regulatory_compliance_analysis,
    generate_glossary,
)


class ReportGenerator:
    """
    Generate HTML and PDF reports from parsed data.
    
    This class uses a modular approach with specialized components for:
    - Chart generation
    - Text content generation
    - Case studies generation
    - Appendices generation
    - Additional content generation
    """

    def __init__(self, template_dir: str):
        """
        Initialize the Report Generator.

        Args:
            template_dir: Path to the directory containing the HTML templates
        """
        self.template_dir = template_dir
        self.env = Environment(loader=FileSystemLoader(template_dir))
        self.charts_dir = "charts"
        
        # Add markdown filter to Jinja environment
        self.env.filters['markdown'] = self._convert_markdown_to_html
        
        # Create charts directory if it doesn't exist
        os.makedirs(self.charts_dir, exist_ok=True)
    
    def _convert_markdown_to_html(self, text: str) -> str:
        """
        Convert markdown text to HTML.
        
        Args:
            text: Markdown text
            
        Returns:
            str: HTML text
        """
        # Convert markdown to HTML with code highlighting
        html = markdown.markdown(
            text,
            extensions=[
                'markdown.extensions.tables',
                'markdown.extensions.fenced_code',
                'markdown.extensions.codehilite',
                'markdown.extensions.nl2br',
                'markdown.extensions.extra'
            ],
            extension_configs={
                'markdown.extensions.codehilite': {
                    'css_class': 'codehilite',
                    'linenums': False,
                    'guess_lang': False
                },
                'markdown.extensions.tables': {
                    'table_class': 'markdown-table'
                }
            }
        )
        
        # Add CSS classes to headings
        html = html.replace('<h1>', '<h1 class="markdown-h1">')
        html = html.replace('<h2>', '<h2 class="markdown-h2">')
        html = html.replace('<h3>', '<h3 class="markdown-h3">')
        html = html.replace('<h4>', '<h4 class="markdown-h4">')
        
        return html

    def generate_report(
        self, parsed_data: Dict[str, Any], output_html: str, output_pdf: str
    ) -> None:
        """
        Generate the HTML and PDF reports from the parsed data.

        Args:
            parsed_data: Parsed data from the input file
            output_html: Path to the output HTML file
            output_pdf: Path to the output PDF file
        """
        try:
            # Generate HTML content
            html_content = self._generate_html_content(parsed_data)
            
            # Save the HTML file
            with open(output_html, "w") as f:
                f.write(html_content)
            
            # Convert HTML to PDF
            HTML(string=html_content, base_url=os.path.abspath(os.path.dirname(os.path.abspath(__file__)))).write_pdf(output_pdf)
        except Exception as e:
            raise Exception(f"Error generating report: {str(e)}")

    def _render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """
        Render a template with the given context.

        Args:
            template_name: Name of the template to render
            context: Context to use for rendering

        Returns:
            str: Rendered template
        """
        template = self.env.get_template(template_name)
        return template.render(**context)
        
    def export_report(self, parsed_data: Dict[str, Any], output_format: str, output_path: str) -> None:
        """
        Export the report in different formats.
        
        Args:
            parsed_data: Parsed data from the input file
            output_format: Format to export the report in (html, pdf, docx)
            output_path: Path to the output file
        
        Raises:
            ValueError: If the output format is not supported
        """
        if output_format not in ["html", "pdf", "docx"]:
            raise ValueError(f"Unsupported output format: {output_format}")
            
        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        if output_format == "html":
            # Generate HTML report
            html_content = self._generate_html_content(parsed_data)
            with open(output_path, "w") as f:
                f.write(html_content)
        elif output_format == "pdf":
            # Generate PDF report
            html_content = self._generate_html_content(parsed_data)
            HTML(string=html_content, base_url=os.path.abspath(os.path.dirname(os.path.abspath(__file__)))).write_pdf(output_path)
        elif output_format == "docx":
            # Generate DOCX report (placeholder for future implementation)
            raise NotImplementedError("DOCX export is not yet implemented")
            
    def _generate_html_content(self, parsed_data: Dict[str, Any]) -> str:
        """
        Generate the HTML content for the report.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            str: HTML content for the report
        """
        # Generate charts
        charts = generate_charts(parsed_data, self.charts_dir)
        
        # Convert relative paths to absolute paths
        charts_absolute = {}
        for chart_name, chart_path in charts.items():
            charts_absolute[chart_name] = os.path.abspath(chart_path)
        
        # Use absolute paths for charts
        charts = charts_absolute
        
        # Generate detailed case studies
        case_studies = generate_case_studies()
        
        # Generate detailed appendices
        appendices = generate_detailed_appendices()
        
        # Add additional context for the templates
        context = {
            "title": f"{parsed_data['model_metadata']['name']} Validation Report",
            "charts": charts,
            "case_studies": case_studies,
            "appendices": appendices,
            # Add additional context for the executive summary
            "executive_summary": {
                "key_findings": generate_key_findings(parsed_data),
                "statistical_performance_summary": generate_statistical_performance_summary(parsed_data),
            },
            # Add additional context for the model overview
            "model_overview": {
                "purpose_description": generate_purpose_description(parsed_data),
                "assumptions_limitations_analysis": generate_assumptions_limitations_analysis(parsed_data),
            },
            # Add additional context for the validation results
            "validation_results": {
                "statistical_performance_analysis": generate_statistical_performance_analysis(parsed_data),
                "stability_analysis": generate_stability_analysis(parsed_data),
                "sensitivity_analysis": generate_sensitivity_analysis(parsed_data),
                "benchmarking_analysis": generate_benchmarking_analysis(parsed_data),
                "conceptual_soundness_analysis": generate_conceptual_soundness_analysis(parsed_data),
            },
            # Add additional context for the risk assessment
            "risk_assessment_summary": {
                "overall_assessment": generate_risk_assessment_overall(parsed_data),
                "methodology": generate_risk_assessment_methodology(),
                "high_risk_criteria": "Critical issues that could significantly impact model performance or compliance.",
                "medium_risk_criteria": "Important issues that should be addressed but do not critically impact model performance.",
                "low_risk_criteria": "Minor issues that have minimal impact on model performance or compliance.",
                "mitigation_strategy": generate_risk_mitigation_strategy(parsed_data),
                "residual_risk": generate_residual_risk(parsed_data),
            },
            # Add additional context for the recommendations
            "recommendations_summary": {
                "introduction": generate_recommendations_introduction(parsed_data),
                "implementation_plan": generate_implementation_plan(parsed_data),
                "expected_benefits": generate_expected_benefits(parsed_data),
                "monitoring_plan": generate_monitoring_plan(parsed_data),
            },
            # Add sample recommendations
            "recommendations": {
                "high_priority": [
                    {
                        "description": "Develop supplementary model for thin-file customers to improve predictive accuracy for this segment",
                        "owner": "Model Development Team",
                        "timeline": "Q3 2023",
                        "status": "In Progress"
                    },
                    {
                        "description": "Implement enhanced monitoring for self-employed applicants with additional performance metrics",
                        "owner": "Model Monitoring Team",
                        "timeline": "Q2 2023",
                        "status": "Completed"
                    }
                ],
                "medium_priority": [
                    {
                        "description": "Update model documentation to include detailed feature engineering process",
                        "owner": "Documentation Team",
                        "timeline": "Q3 2023",
                        "status": "Not Started"
                    },
                    {
                        "description": "Enhance stress testing framework to include more granular economic scenarios",
                        "owner": "Risk Analytics Team",
                        "timeline": "Q4 2023",
                        "status": "In Progress"
                    },
                    {
                        "description": "Implement automated alerts for population stability index exceeding thresholds",
                        "owner": "Model Monitoring Team",
                        "timeline": "Q3 2023",
                        "status": "Not Started"
                    }
                ],
                "low_priority": [
                    {
                        "description": "Explore alternative data sources for improving model performance",
                        "owner": "Data Science Team",
                        "timeline": "Q1 2024",
                        "status": "Not Started"
                    },
                    {
                        "description": "Develop interactive dashboard for model performance visualization",
                        "owner": "Reporting Team",
                        "timeline": "Q2 2024",
                        "status": "Not Started"
                    }
                ]
            },
            # Add additional context for the supporting data
            "supporting_data_analysis": {
                "segment_performance": generate_segment_performance_analysis(parsed_data),
                "calibration": generate_calibration_analysis(parsed_data),
                "feature_importance": generate_feature_importance_analysis(parsed_data),
                "backtesting": generate_backtesting_analysis(parsed_data),
                "regulatory_compliance": generate_regulatory_compliance_analysis(parsed_data),
            },
            # Add additional context for the appendix
            "appendix": {
                "glossary": generate_glossary(),
                "methodology": {
                    "development_process": "The model was developed following a structured process that included data collection, feature engineering, model selection, and validation.",
                    "feature_engineering": "Features were engineered based on domain expertise and statistical analysis.",
                    "model_selection": "Multiple model architectures were evaluated, and the final model was selected based on performance metrics.",
                    "hyperparameter_tuning": "Hyperparameters were tuned using grid search with cross-validation.",
                    "training_validation_approach": "The model was trained on historical data from 2018-2022 and validated on out-of-time data.",
                },
                "data": {
                    "sources": "Data was sourced from internal systems, credit bureaus, and macroeconomic indicators.",
                    "quality_checks": "Data quality checks included missing value analysis, outlier detection, and consistency checks.",
                    "preprocessing": "Data preprocessing included imputation, normalization, and encoding of categorical variables.",
                },
                "validation": {
                    "statistical_tests": "Statistical tests included hypothesis testing, correlation analysis, and distribution comparisons.",
                    "sensitivity_analysis": "Sensitivity analysis was performed by varying input parameters and observing the impact on model outputs.",
                    "stress_testing_scenarios": [
                        {
                            "name": "Severe Recession",
                            "description": "Unemployment rate increases by 5%, housing prices drop by 20%",
                            "impact": "12% degradation in model performance",
                        },
                        {
                            "name": "Moderate Recession",
                            "description": "Unemployment rate increases by 2%, housing prices drop by 10%",
                            "impact": "5% degradation in model performance",
                        },
                        {
                            "name": "Inflation Spike",
                            "description": "Inflation increases to 8%, interest rates rise by 2%",
                            "impact": "3% degradation in model performance",
                        },
                    ],
                },
                "regulatory": {
                    "sr_11_7": "SR 11-7 provides guidance on model risk management, including model development, implementation, and use.",
                    "fair_lending": "Fair lending regulations prohibit discrimination in lending practices.",
                    "cecl": "CECL requires estimation of expected credit losses over the life of a loan.",
                },
                "references": [
                    "Federal Reserve SR 11-7: Guidance on Model Risk Management",
                    "Basel Committee on Banking Supervision: Guidance on Credit Risk and Accounting for Expected Credit Losses",
                    "Office of the Comptroller of the Currency: Model Risk Management Guidance",
                ],
            },
            # Add MRM approver for the approval section
            "approval_information": {
                **parsed_data["approval_information"],
                "mrm_approver": "John Smith, Head of Model Risk Management",
            },
        }
        
        # Merge the parsed data with the additional context
        context = {**parsed_data, **context}
        
        # Render the HTML template
        return self._render_template("report.html", context)
