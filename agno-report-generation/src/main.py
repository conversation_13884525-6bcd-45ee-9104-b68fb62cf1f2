"""
Main script for the MRM Report Generation System.

This script demonstrates how to use the Data Parser Agent to parse the input file
and extract structured information, and the Report Generator to generate HTML and PDF reports.
"""

import argparse
import json
import os
import sys
from typing import Dict, Any

from .agents.data_parser_agent import DataParserAgent
from .report_generator import ReportGenerator


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="MRM Report Generation System")
    parser.add_argument(
        "--input", "-i", type=str, required=True, help="Path to the input text file"
    )
    parser.add_argument(
        "--output", "-o", type=str, help="Path to the output JSON file (default: parsed_data.json)"
    )
    parser.add_argument(
        "--api-key", type=str, help="Anthropic API key (default: from ANTHROPIC_API_KEY env var)"
    )
    parser.add_argument(
        "--use-agent", action="store_true", help="Use the Agno agent for parsing (default: use rule-based parser)"
    )
    parser.add_argument(
        "--generate-report", action="store_true", help="Generate HTML and PDF reports from the parsed data"
    )
    parser.add_argument(
        "--html-output", type=str, help="Path to the output HTML file (default: report.html)"
    )
    parser.add_argument(
        "--pdf-output", type=str, help="Path to the output PDF file (default: report.pdf)"
    )
    parser.add_argument(
        "--template-dir", type=str, help="Path to the directory containing the HTML templates (default: templates)"
    )
    parser.add_argument(
        "--format", "-f", type=str, choices=["html", "pdf", "docx"], 
        help="Export format (html, pdf, docx). If specified, uses export_report instead of generate_report"
    )
    parser.add_argument(
        "--output-path", type=str, help="Path to the output file when using --format"
    )
    return parser.parse_args()


def main() -> None:
    """Main function."""
    # Parse command line arguments
    args = parse_args()
    
    # Get input file path
    input_file = args.input
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' does not exist")
        sys.exit(1)
    
    # Get output file path
    output_file = args.output or "parsed_data.json"
    
    # Get API key
    api_key = args.api_key or os.environ.get("ANTHROPIC_API_KEY")
    if not api_key:
        print("Error: Anthropic API key not provided")
        print("Please provide it via --api-key or set the ANTHROPIC_API_KEY environment variable")
        sys.exit(1)
    
    # Create the Data Parser Agent
    data_parser = DataParserAgent(api_key=api_key)
    
    # Parse the input file
    print(f"Parsing input file: {input_file}")
    try:
        if args.use_agent:
            print("Using Agno agent for parsing...")
            parsed_data = data_parser.parse_with_agent(input_file)
        else:
            print("Using rule-based parser...")
            parsed_data = data_parser.parse_file(input_file)
    except Exception as e:
        print(f"Error parsing input file: {str(e)}")
        sys.exit(1)
    
    # Convert the parsed data to a dictionary
    parsed_data_dict = parsed_data.model_dump()
    
    # Save the parsed data to the output file
    print(f"Saving parsed data to: {output_file}")
    try:
        with open(output_file, "w") as f:
            json.dump(parsed_data_dict, f, indent=2)
    except Exception as e:
        print(f"Error saving parsed data: {str(e)}")
        sys.exit(1)
    
    # Get template directory
    template_dir = args.template_dir or "templates"
    if not os.path.exists(template_dir):
        print(f"Error: Template directory '{template_dir}' does not exist")
        sys.exit(1)
    
    # Create the Report Generator
    report_generator = ReportGenerator(template_dir=template_dir)
    
    # Generate reports based on the specified options
    if args.format:
        # Export report in the specified format
        output_path = args.output_path or f"report.{args.format}"
        print(f"Exporting report in {args.format.upper()} format: {output_path}")
        try:
            report_generator.export_report(
                parsed_data=parsed_data_dict,
                output_format=args.format,
                output_path=output_path
            )
        except NotImplementedError as e:
            print(f"Error: {str(e)}")
            sys.exit(1)
        except Exception as e:
            print(f"Error exporting report: {str(e)}")
            sys.exit(1)
    elif args.generate_report:
        # Generate HTML and PDF reports
        html_output = args.html_output or "report.html"
        pdf_output = args.pdf_output or "report.pdf"
        
        # Generate the reports
        print(f"Generating HTML report: {html_output}")
        print(f"Generating PDF report: {pdf_output}")
        try:
            report_generator.generate_report(
                parsed_data=parsed_data_dict,
                output_html=html_output,
                output_pdf=pdf_output
            )
        except Exception as e:
            print(f"Error generating reports: {str(e)}")
            sys.exit(1)
    
    print("Done!")


if __name__ == "__main__":
    main()
