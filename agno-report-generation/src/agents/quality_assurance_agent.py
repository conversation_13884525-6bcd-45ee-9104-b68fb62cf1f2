"""
Quality Assurance Agent for the MRM Report Generation System.

This agent is responsible for reviewing the report for accuracy, consistency, and quality,
identifying issues, and suggesting improvements.
"""

import os
from typing import Dict, Any, List, Tuple

from agno import Agent, Message, AgentConfig
from agno.tools import Tool, ToolConfig


class QualityAssuranceAgent:
    """
    Quality Assurance Agent for the MRM Report Generation System.
    
    This agent reviews the report for quality by:
    1. Checking for accuracy and factual correctness
    2. Ensuring consistency across sections
    3. Identifying gaps and missing information
    4. Suggesting improvements for clarity and readability
    5. Validating technical content and methodology descriptions
    """
    
    def __init__(self, api_key: str, model: str = "claude-3-opus-20240229"):
        """
        Initialize the Quality Assurance Agent.
        
        Args:
            api_key: Anthropic API key
            model: Model to use for the agent
        """
        self.api_key = api_key
        self.model = model
        
        # Create the agent
        self.agent = Agent(
            config=AgentConfig(
                name="Quality Assurance Agent",
                description="Reviews model validation reports for accuracy, consistency, and quality",
                model=self.model,
                api_key=self.api_key
            )
        )
    
    def review_report(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Review the report for quality issues.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: Quality review results
        """
        # Check for accuracy
        accuracy_review = self._check_accuracy(parsed_data)
        
        # Check for consistency
        consistency_review = self._check_consistency(parsed_data)
        
        # Check for completeness
        completeness_review = self._check_completeness(parsed_data)
        
        # Check for clarity and readability
        clarity_review = self._check_clarity(parsed_data)
        
        # Check for technical validity
        technical_review = self._check_technical_validity(parsed_data)
        
        # Combine all reviews
        quality_review = {
            "accuracy": accuracy_review,
            "consistency": consistency_review,
            "completeness": completeness_review,
            "clarity": clarity_review,
            "technical_validity": technical_review,
            "overall_assessment": self._assess_overall_quality(
                accuracy_review,
                consistency_review,
                completeness_review,
                clarity_review,
                technical_review
            ),
            "improvement_suggestions": self._generate_improvement_suggestions(
                accuracy_review,
                consistency_review,
                completeness_review,
                clarity_review,
                technical_review
            )
        }
        
        return quality_review
    
    def _check_accuracy(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check the report for accuracy and factual correctness.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: Accuracy review results
        """
        # Extract relevant information from the parsed data
        model_metadata = parsed_data.get("model_metadata", {})
        model_methodology = parsed_data.get("model_methodology", {})
        validation_tests = parsed_data.get("validation_tests", {})
        
        # Prepare the prompt for the agent
        prompt = f"""
        Review the following model validation report for accuracy and factual correctness:
        
        Model Information:
        - Name: {model_metadata.get("name", "")}
        - Version: {model_metadata.get("version", "")}
        - Type: {model_methodology.get("model_type", "")}
        - Purpose: {model_methodology.get("description", "")}
        
        Model Methodology:
        - Description: {model_methodology.get("description", "")}
        - Feature Selection: {model_methodology.get("feature_selection", "")}
        - Model Training: {model_methodology.get("model_training", "")}
        
        Validation Tests:
        - Statistical Performance: {validation_tests.get("statistical_performance", {})}
        - Stability Analysis: {validation_tests.get("stability_analysis", {})}
        - Sensitivity Analysis: {validation_tests.get("sensitivity_analysis", {})}
        - Benchmarking: {validation_tests.get("benchmarking", {})}
        - Conceptual Soundness: {validation_tests.get("conceptual_soundness", {})}
        
        Check for the following types of accuracy issues:
        1. Factual errors or incorrect statements
        2. Misrepresentation of model capabilities or limitations
        3. Incorrect or misleading performance metrics
        4. Mathematical or statistical errors
        5. Misalignment between model type and described methodology
        
        For each issue found, provide:
        - The specific section or statement containing the issue
        - A description of the accuracy problem
        - A suggested correction
        
        Also provide an overall assessment of the report's accuracy on a scale of:
        - High: No significant accuracy issues found
        - Medium: Some minor accuracy issues found
        - Low: Significant accuracy issues found
        """
        
        # Get the accuracy review from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        accuracy_review = self._parse_review_response(response.content)
        
        return accuracy_review
    
    def _check_consistency(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check the report for consistency across sections.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: Consistency review results
        """
        # Extract relevant information from the parsed data
        model_metadata = parsed_data.get("model_metadata", {})
        model_methodology = parsed_data.get("model_methodology", {})
        validation_tests = parsed_data.get("validation_tests", {})
        risk_assessment = parsed_data.get("risk_assessment", {})
        recommendations = parsed_data.get("recommendations", {})
        
        # Prepare the prompt for the agent
        prompt = f"""
        Review the following model validation report for consistency across sections:
        
        Model Information:
        - Name: {model_metadata.get("name", "")}
        - Version: {model_metadata.get("version", "")}
        - Type: {model_methodology.get("model_type", "")}
        - Purpose: {model_methodology.get("description", "")}
        
        Model Methodology:
        - Description: {model_methodology.get("description", "")}
        - Feature Selection: {model_methodology.get("feature_selection", "")}
        - Model Training: {model_methodology.get("model_training", "")}
        
        Validation Tests:
        - Statistical Performance: {validation_tests.get("statistical_performance", {})}
        - Stability Analysis: {validation_tests.get("stability_analysis", {})}
        - Sensitivity Analysis: {validation_tests.get("sensitivity_analysis", {})}
        - Benchmarking: {validation_tests.get("benchmarking", {})}
        - Conceptual Soundness: {validation_tests.get("conceptual_soundness", {})}
        
        Risk Assessment:
        - Overall Risk Rating: {risk_assessment.get("overall_risk_rating", "")}
        - Risk Factors: {risk_assessment.get("risk_factors", [])}
        
        Recommendations:
        - High Priority: {recommendations.get("high_priority", [])}
        - Medium Priority: {recommendations.get("medium_priority", [])}
        - Low Priority: {recommendations.get("low_priority", [])}
        
        Check for the following types of consistency issues:
        1. Contradictory statements or findings across sections
        2. Inconsistent terminology or definitions
        3. Misalignment between findings and recommendations
        4. Inconsistent risk ratings or assessments
        5. Discrepancies in reported metrics or results
        
        For each issue found, provide:
        - The specific sections containing the inconsistency
        - A description of the consistency problem
        - A suggested resolution
        
        Also provide an overall assessment of the report's consistency on a scale of:
        - High: No significant consistency issues found
        - Medium: Some minor consistency issues found
        - Low: Significant consistency issues found
        """
        
        # Get the consistency review from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        consistency_review = self._parse_review_response(response.content)
        
        return consistency_review
    
    def _check_completeness(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check the report for completeness and identify gaps.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: Completeness review results
        """
        # Define the required sections and subsections for a complete report
        required_sections = {
            "model_metadata": ["name", "version", "type", "owner", "approval_date"],
            "model_methodology": ["description", "feature_selection", "model_training"],
            "validation_tests": ["statistical_performance", "stability_analysis", "sensitivity_analysis", "benchmarking", "conceptual_soundness"],
            "risk_assessment": ["overall_risk_rating", "risk_factors"],
            "recommendations": ["high_priority", "medium_priority", "low_priority"],
            "validation_team": ["lead", "members"],
            "approval_information": ["approver", "approval_date", "next_review_date"]
        }
        
        # Check if each required section and subsection is present and non-empty
        missing_sections = []
        incomplete_sections = []
        
        for section, subsections in required_sections.items():
            if section not in parsed_data:
                missing_sections.append(section)
            elif not parsed_data[section]:
                incomplete_sections.append(section)
            else:
                for subsection in subsections:
                    if subsection not in parsed_data[section] or not parsed_data[section][subsection]:
                        incomplete_sections.append(f"{section}.{subsection}")
        
        # Prepare the prompt for the agent
        prompt = f"""
        Review the following model validation report for completeness:
        
        Missing Sections: {missing_sections}
        Incomplete Sections: {incomplete_sections}
        
        A complete model validation report should include:
        1. Model metadata (name, version, type, owner, approval date)
        2. Model methodology (description, feature selection, training approach)
        3. Validation tests (statistical performance, stability, sensitivity, benchmarking, conceptual soundness)
        4. Risk assessment (overall rating, risk factors)
        5. Recommendations (high, medium, low priority)
        6. Validation team information (lead, members)
        7. Approval information (approver, approval date, next review date)
        
        Check for the following types of completeness issues:
        1. Missing required sections or subsections
        2. Incomplete or insufficient information within sections
        3. Lack of detail in critical areas
        4. Missing supporting evidence or examples
        5. Inadequate coverage of important aspects of the model
        
        For each issue found, provide:
        - The specific section or subsection with the completeness issue
        - A description of what is missing or incomplete
        - A suggested addition or enhancement
        
        Also provide an overall assessment of the report's completeness on a scale of:
        - High: No significant completeness issues found
        - Medium: Some minor completeness issues found
        - Low: Significant completeness issues found
        """
        
        # Get the completeness review from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        completeness_review = self._parse_review_response(response.content)
        
        return completeness_review
    
    def _check_clarity(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check the report for clarity and readability.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: Clarity review results
        """
        # Extract relevant information from the parsed data
        model_metadata = parsed_data.get("model_metadata", {})
        model_methodology = parsed_data.get("model_methodology", {})
        validation_tests = parsed_data.get("validation_tests", {})
        risk_assessment = parsed_data.get("risk_assessment", {})
        recommendations = parsed_data.get("recommendations", {})
        
        # Prepare the prompt for the agent
        prompt = f"""
        Review the following model validation report for clarity and readability:
        
        Model Information:
        - Name: {model_metadata.get("name", "")}
        - Version: {model_metadata.get("version", "")}
        - Type: {model_methodology.get("model_type", "")}
        - Purpose: {model_methodology.get("description", "")}
        
        Model Methodology:
        - Description: {model_methodology.get("description", "")}
        - Feature Selection: {model_methodology.get("feature_selection", "")}
        - Model Training: {model_methodology.get("model_training", "")}
        
        Validation Tests:
        - Statistical Performance: {validation_tests.get("statistical_performance", {})}
        - Stability Analysis: {validation_tests.get("stability_analysis", {})}
        - Sensitivity Analysis: {validation_tests.get("sensitivity_analysis", {})}
        - Benchmarking: {validation_tests.get("benchmarking", {})}
        - Conceptual Soundness: {validation_tests.get("conceptual_soundness", {})}
        
        Risk Assessment:
        - Overall Risk Rating: {risk_assessment.get("overall_risk_rating", "")}
        - Risk Factors: {risk_assessment.get("risk_factors", [])}
        
        Recommendations:
        - High Priority: {recommendations.get("high_priority", [])}
        - Medium Priority: {recommendations.get("medium_priority", [])}
        - Low Priority: {recommendations.get("low_priority", [])}
        
        Check for the following types of clarity issues:
        1. Unclear or ambiguous language
        2. Excessive jargon or technical terms without explanation
        3. Complex sentence structures that hinder understanding
        4. Poor organization or flow of information
        5. Lack of context or background information
        
        For each issue found, provide:
        - The specific section or statement with the clarity issue
        - A description of the clarity problem
        - A suggested rewording or restructuring
        
        Also provide an overall assessment of the report's clarity on a scale of:
        - High: No significant clarity issues found
        - Medium: Some minor clarity issues found
        - Low: Significant clarity issues found
        """
        
        # Get the clarity review from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        clarity_review = self._parse_review_response(response.content)
        
        return clarity_review
    
    def _check_technical_validity(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check the report for technical validity.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: Technical validity review results
        """
        # Extract relevant information from the parsed data
        model_methodology = parsed_data.get("model_methodology", {})
        validation_tests = parsed_data.get("validation_tests", {})
        
        # Prepare the prompt for the agent
        prompt = f"""
        Review the following model validation report for technical validity:
        
        Model Methodology:
        - Model Type: {model_methodology.get("model_type", "")}
        - Description: {model_methodology.get("description", "")}
        - Feature Selection: {model_methodology.get("feature_selection", "")}
        - Model Training: {model_methodology.get("model_training", "")}
        
        Validation Tests:
        - Statistical Performance: {validation_tests.get("statistical_performance", {})}
        - Stability Analysis: {validation_tests.get("stability_analysis", {})}
        - Sensitivity Analysis: {validation_tests.get("sensitivity_analysis", {})}
        - Benchmarking: {validation_tests.get("benchmarking", {})}
        - Conceptual Soundness: {validation_tests.get("conceptual_soundness", {})}
        
        Check for the following types of technical validity issues:
        1. Inappropriate validation techniques for the model type
        2. Misapplication of statistical methods or tests
        3. Invalid assumptions or methodologies
        4. Incorrect interpretation of results
        5. Inadequate validation scope for the model's purpose and risk
        
        For each issue found, provide:
        - The specific section or statement with the technical validity issue
        - A description of the technical problem
        - A suggested correction or improvement
        
        Also provide an overall assessment of the report's technical validity on a scale of:
        - High: No significant technical validity issues found
        - Medium: Some minor technical validity issues found
        - Low: Significant technical validity issues found
        """
        
        # Get the technical validity review from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        technical_review = self._parse_review_response(response.content)
        
        return technical_review
    
    def _assess_overall_quality(
        self,
        accuracy_review: Dict[str, Any],
        consistency_review: Dict[str, Any],
        completeness_review: Dict[str, Any],
        clarity_review: Dict[str, Any],
        technical_review: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Assess overall quality based on individual quality reviews.
        
        Args:
            accuracy_review: Accuracy review results
            consistency_review: Consistency review results
            completeness_review: Completeness review results
            clarity_review: Clarity review results
            technical_review: Technical validity review results
            
        Returns:
            Dict[str, Any]: Overall quality assessment
        """
        # Extract overall assessments from each review
        accuracy_overall = accuracy_review.get("overall_assessment", "")
        consistency_overall = consistency_review.get("overall_assessment", "")
        completeness_overall = completeness_review.get("overall_assessment", "")
        clarity_overall = clarity_review.get("overall_assessment", "")
        technical_overall = technical_review.get("overall_assessment", "")
        
        # Extract issues from each review
        accuracy_issues = accuracy_review.get("issues", [])
        consistency_issues = consistency_review.get("issues", [])
        completeness_issues = completeness_review.get("issues", [])
        clarity_issues = clarity_review.get("issues", [])
        technical_issues = technical_review.get("issues", [])
        
        # Prepare the prompt for the agent
        prompt = f"""
        Assess the overall quality of a model validation report based on the following individual assessments:
        
        Accuracy: {accuracy_overall}
        - Issues: {len(accuracy_issues)} found
        
        Consistency: {consistency_overall}
        - Issues: {len(consistency_issues)} found
        
        Completeness: {completeness_overall}
        - Issues: {len(completeness_issues)} found
        
        Clarity: {clarity_overall}
        - Issues: {len(clarity_issues)} found
        
        Technical Validity: {technical_overall}
        - Issues: {len(technical_issues)} found
        
        Provide an overall quality assessment that considers all of these individual assessments.
        The overall assessment should be one of:
        - High Quality: The report is of high quality with few or no issues
        - Medium Quality: The report is of acceptable quality with some issues that should be addressed
        - Low Quality: The report has significant quality issues that must be addressed
        
        Provide a detailed explanation for your overall assessment, highlighting the key strengths and weaknesses of the report from a quality perspective.
        """
        
        # Get the overall quality assessment from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        overall_assessment = {
            "rating": self._extract_quality_rating(response.content),
            "explanation": response.content
        }
        
        return overall_assessment
    
    def _generate_improvement_suggestions(
        self,
        accuracy_review: Dict[str, Any],
        consistency_review: Dict[str, Any],
        completeness_review: Dict[str, Any],
        clarity_review: Dict[str, Any],
        technical_review: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate improvement suggestions based on quality reviews.
        
        Args:
            accuracy_review: Accuracy review results
            consistency_review: Consistency review results
            completeness_review: Completeness review results
            clarity_review: Clarity review results
            technical_review: Technical validity review results
            
        Returns:
            List[Dict[str, Any]]: List of improvement suggestions
        """
        # Extract issues from each review
        accuracy_issues = accuracy_review.get("issues", [])
        consistency_issues = consistency_review.get("issues", [])
        completeness_issues = completeness_review.get("issues", [])
        clarity_issues = clarity_review.get("issues", [])
        technical_issues = technical_review.get("issues", [])
        
        # Combine all issues
        all_issues = []
        for issue in accuracy_issues:
            all_issues.append({**issue, "type": "Accuracy"})
        for issue in consistency_issues:
            all_issues.append({**issue, "type": "Consistency"})
        for issue in completeness_issues:
            all_issues.append({**issue, "type": "Completeness"})
        for issue in clarity_issues:
            all_issues.append({**issue, "type": "Clarity"})
        for issue in technical_issues:
            all_issues.append({**issue, "type": "Technical Validity"})
        
        # Prepare the prompt for the agent
        prompt = f"""
        Generate improvement suggestions for a model validation report based on the following issues:
        
        Issues:
        {all_issues}
        
        For each issue, provide a specific, actionable suggestion for improvement.
        Prioritize the suggestions based on their importance and impact on the overall quality of the report.
        
        For each suggestion, include:
        1. The section or area to improve
        2. The specific issue to address
        3. A detailed recommendation for how to address it
        4. The expected benefit of implementing the suggestion
        
        Limit your response to the top 10 most important suggestions.
        """
        
        # Get the improvement suggestions from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        suggestions = self._parse_suggestions_response(response.content)
        
        return suggestions
    
    def _parse_review_response(self, response: str) -> Dict[str, Any]:
        """
        Parse the review response from the agent into structured content.
        
        Args:
            response: Response from the agent
            
        Returns:
            Dict[str, Any]: Structured review results
        """
        # This is a simplified parser that assumes the response is in a specific format
        # In a real implementation, this would be more robust
        sections = {}
        current_section = None
        current_content = []
        
        # Split the response into lines
        lines = response.strip().split("\n")
        
        for line in lines:
            line = line.strip()
            
            # Skip empty lines
            if not line:
                continue
            
            # Check if this is a heading (assuming headings end with a colon)
            if line.endswith(":"):
                # Save the previous section
                if current_section is not None:
                    sections[current_section] = "\n".join(current_content)
                
                # Start a new section
                current_section = line[:-1]  # Remove the colon
                current_content = []
            else:
                # Add to the current section
                if current_section is not None:
                    current_content.append(line)
        
        # Save the last section
        if current_section is not None:
            sections[current_section] = "\n".join(current_content)
        
        # Extract the overall assessment
        overall_assessment = sections.get("Overall Assessment", "")
        
        # Extract issues
        issues = []
        for section, content in sections.items():
            if section.startswith("Issue "):
                issue = {
                    "id": section,
                    "description": content
                }
                issues.append(issue)
        
        # Structure the review results
        review_results = {
            "issues": issues,
            "overall_assessment": overall_assessment,
            "full_text": response
        }
        
        return review_results
    
    def _parse_suggestions_response(self, response: str) -> List[Dict[str, Any]]:
        """
        Parse the suggestions response from the agent into structured content.
        
        Args:
            response: Response from the agent
            
        Returns:
            List[Dict[str, Any]]: List of improvement suggestions
        """
        # This is a simplified parser that assumes the response is in a specific format
        # In a real implementation, this would be more robust
        suggestions = []
        current_suggestion = None
        current_content = []
        
        # Split the response into lines
        lines = response.strip().split("\n")
        
        for line in lines:
            line = line.strip()
            
            # Skip empty lines
            if not line:
                continue
            
            # Check if this is a new suggestion (assuming suggestions start with a number followed by a period)
            if line[0].isdigit() and ". " in line[:5]:
                # Save the previous suggestion
                if current_suggestion is not None:
                    suggestions.append({
                        "id": current_suggestion,
                        "content": "\n".join(current_content)
                    })
                
                # Start a new suggestion
                current_suggestion = line.split(". ")[0]
                current_content = [line.split(". ", 1)[1]]
            else:
                # Add to the current suggestion
                if current_suggestion is not None:
                    current_content.append(line)
        
        # Save the last suggestion
        if current_suggestion is not None:
            suggestions.append({
                "id": current_suggestion,
                "content": "\n".join(current_content)
            })
        
        return suggestions
    
    def _extract_quality_rating(self, text: str) -> str:
        """
        Extract the quality rating from the text.
        
        Args:
            text: Text to extract the rating from
            
        Returns:
            str: Quality rating
        """
        # Look for common quality ratings in the text
        if "high quality" in text.lower():
            return "High Quality"
        elif "medium quality" in text.lower():
            return "Medium Quality"
        elif "low quality" in text.lower():
            return "Low Quality"
        else:
            return "Unknown"
