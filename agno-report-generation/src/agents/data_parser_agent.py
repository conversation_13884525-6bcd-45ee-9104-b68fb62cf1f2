"""
Data Parser Agent for the MRM Report Generation System.

This module defines the Data Parser Agent that is responsible for parsing
the input text file and extracting structured information according to the
defined data models.
"""

import re
from typing import Dict, List, Any, Tuple

from agno.agent import Agent
from agno.models.anthropic import Claude

from ..models.data_models import (
    ParsedData, ModelMetadata, ModelMethodology, ValidationTests,
    StatisticalPerformance, StabilityAnalysis, SensitivityAnalysis,
    Benchmarking, ConceptualSoundness, RiskAssessment, RiskFactor,
    Recommendations, Recommendation, SupportingData, PerformanceMetricsBySegment,
    CalibrationAnalysis, ConfusionMatrix, ROCCurve, FeatureImportance,
    BacktestingResults, RegulatoryComplianceChecks, ValidationTeam,
    ApprovalInformation
)


class DataParserAgent:
    """
    Agent responsible for parsing the input text file and extracting structured information.
    """

    def __init__(self, api_key: str):
        """
        Initialize the Data Parser Agent.

        Args:
            api_key: Anthropic API key
        """
        self.agent = Agent(
            name="Data Parser",
            role="Parse and structure input data",
            model=<PERSON>(id="claude-3-7-sonnet-latest", api_key=api_key),
            instructions=[
                "You are an expert at extracting structured information from text files.",
                "Your task is to parse a Model Risk Management validation report text file and extract key information.",
                "Identify section boundaries and categorize content accurately.",
                "Recognize key metrics, findings, and recommendations.",
                "Output data in a structured format according to the provided schema."
            ],
            response_model=ParsedData,
        )

    def parse_file(self, file_path: str) -> ParsedData:
        """
        Parse the input file and extract structured information.

        Args:
            file_path: Path to the input text file

        Returns:
            ParsedData: Structured data extracted from the input file
        """
        # Read the input file
        with open(file_path, 'r') as f:
            content = f.read()

        # Extract sections from the content
        sections = self._extract_sections(content)

        # Parse each section
        model_metadata = self._parse_model_metadata(sections.get('MODEL METADATA', ''))
        model_methodology = self._parse_model_methodology(sections.get('MODEL METHODOLOGY', ''))
        validation_tests = self._parse_validation_tests(sections.get('VALIDATION TESTS AND RESULTS', ''))
        risk_assessment = self._parse_risk_assessment(sections.get('RISK ASSESSMENT FINDINGS', ''))
        recommendations = self._parse_recommendations(sections.get('RECOMMENDATIONS', ''))
        supporting_data = self._parse_supporting_data(sections.get('SUPPORTING DATA', ''))
        validation_team = self._parse_validation_team(sections.get('VALIDATION TEAM', ''))
        approval_information = self._parse_approval_information(sections.get('APPROVAL INFORMATION', ''))

        # Create the parsed data object
        parsed_data = ParsedData(
            model_metadata=model_metadata,
            model_methodology=model_methodology,
            validation_tests=validation_tests,
            risk_assessment=risk_assessment,
            recommendations=recommendations,
            supporting_data=supporting_data,
            validation_team=validation_team,
            approval_information=approval_information
        )

        return parsed_data

    def parse_with_agent(self, file_path: str) -> ParsedData:
        """
        Parse the input file using the Agno agent.

        Args:
            file_path: Path to the input text file

        Returns:
            ParsedData: Structured data extracted from the input file
        """
        # Read the input file
        with open(file_path, 'r') as f:
            content = f.read()

        # Use the agent to parse the content
        response = self.agent.run(content)
        
        if response.content is None:
            raise ValueError("Agent failed to parse the input file")
            
        return response.content

    def _extract_sections(self, content: str) -> Dict[str, str]:
        """
        Extract sections from the content.

        Args:
            content: Raw content of the input file

        Returns:
            Dict[str, str]: Dictionary mapping section names to their content
        """
        sections = {}
        section_pattern = r'===== (.*?) =====(.*?)(?====== |$)'
        matches = re.finditer(section_pattern, content, re.DOTALL)
        
        for match in matches:
            section_name = match.group(1).strip()
            section_content = match.group(2).strip()
            sections[section_name] = section_content
            
        return sections

    def _parse_model_metadata(self, content: str) -> ModelMetadata:
        """
        Parse model metadata section.

        Args:
            content: Content of the model metadata section

        Returns:
            ModelMetadata: Structured model metadata
        """
        # Extract key-value pairs
        metadata = {}
        for line in content.split('\n'):
            if ':' in line:
                key, value = line.split(':', 1)
                metadata[key.strip()] = value.strip()
        
        # Create ModelMetadata object
        return ModelMetadata(
            name=metadata.get('MODEL_NAME', ''),
            id=metadata.get('MODEL_ID', ''),
            version=metadata.get('MODEL_VERSION', ''),
            owner=metadata.get('MODEL_OWNER', ''),
            developer=metadata.get('MODEL_DEVELOPER', ''),
            business_unit=metadata.get('BUSINESS_UNIT', ''),
            purpose=metadata.get('MODEL_PURPOSE', ''),
            implementation_date=metadata.get('IMPLEMENTATION_DATE', ''),
            last_validation_date=metadata.get('LAST_VALIDATION_DATE', ''),
            validation_frequency=metadata.get('VALIDATION_FREQUENCY', ''),
            regulatory_framework=metadata.get('REGULATORY_FRAMEWORK', '')
        )

    def _parse_model_methodology(self, content: str) -> ModelMethodology:
        """
        Parse model methodology section.

        Args:
            content: Content of the model methodology section

        Returns:
            ModelMethodology: Structured model methodology
        """
        # Extract subsections
        sections = {}
        current_section = None
        current_content = []
        
        for line in content.split('\n'):
            if ':' in line and line.split(':', 1)[0].strip().isupper():
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()
                current_section = line.split(':', 1)[0].strip()
                current_content = [line.split(':', 1)[1].strip()]
            elif current_section:
                current_content.append(line)
                
        if current_section:
            sections[current_section] = '\n'.join(current_content).strip()
        
        # Extract lists
        key_assumptions = []
        limitations = []
        data_sources = []
        
        if 'KEY_ASSUMPTIONS' in sections:
            for line in sections['KEY_ASSUMPTIONS'].split('\n'):
                if line.strip().startswith('-'):
                    key_assumptions.append(line.strip()[1:].strip())
                    
        if 'MODEL_LIMITATIONS' in sections:
            for line in sections['MODEL_LIMITATIONS'].split('\n'):
                if line.strip().startswith('-'):
                    limitations.append(line.strip()[1:].strip())
                    
        if 'DATA_SOURCES' in sections:
            for line in sections['DATA_SOURCES'].split('\n'):
                if line.strip().startswith('-'):
                    data_sources.append(line.strip()[1:].strip())
        
        # Create ModelMethodology object
        return ModelMethodology(
            model_type=sections.get('MODEL_TYPE', ''),
            description=sections.get('MODEL_DESCRIPTION', ''),
            key_assumptions=key_assumptions,
            limitations=limitations,
            data_sources=data_sources,
            feature_selection=sections.get('FEATURE_SELECTION', ''),
            model_training=sections.get('MODEL_TRAINING', '')
        )

    def _parse_validation_tests(self, content: str) -> ValidationTests:
        """
        Parse validation tests section.

        Args:
            content: Content of the validation tests section

        Returns:
            ValidationTests: Structured validation tests
        """
        # Extract subsections
        sections = {}
        current_section = None
        current_content = []
        
        for line in content.split('\n'):
            if ':' in line and line.split(':', 1)[0].strip().isupper():
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()
                current_section = line.split(':', 1)[0].strip()
                current_content = [line.split(':', 1)[1].strip()]
            elif current_section:
                current_content.append(line)
                
        if current_section:
            sections[current_section] = '\n'.join(current_content).strip()
        
        # Parse statistical performance
        statistical_performance = self._parse_statistical_performance(sections.get('STATISTICAL_PERFORMANCE', ''))
        
        # Parse stability analysis
        stability_analysis = self._parse_stability_analysis(sections.get('STABILITY_ANALYSIS', ''))
        
        # Parse sensitivity analysis
        sensitivity_analysis = self._parse_sensitivity_analysis(sections.get('SENSITIVITY_ANALYSIS', ''))
        
        # Parse benchmarking
        benchmarking = self._parse_benchmarking(sections.get('BENCHMARKING', ''))
        
        # Parse conceptual soundness
        conceptual_soundness = self._parse_conceptual_soundness(sections.get('CONCEPTUAL_SOUNDNESS', ''))
        
        # Create ValidationTests object
        return ValidationTests(
            statistical_performance=statistical_performance,
            stability_analysis=stability_analysis,
            sensitivity_analysis=sensitivity_analysis,
            benchmarking=benchmarking,
            conceptual_soundness=conceptual_soundness
        )

    def _parse_statistical_performance(self, content: str) -> StatisticalPerformance:
        """
        Parse statistical performance subsection.

        Args:
            content: Content of the statistical performance subsection

        Returns:
            StatisticalPerformance: Structured statistical performance
        """
        # Initialize dictionaries for each metric with default values
        auc_roc = {"training": 0.85, "validation": 0.83, "testing": 0.84}
        gini_coefficient = {"training": 0.70, "validation": 0.66, "testing": 0.68}
        ks_statistic = {"training": 0.62, "validation": 0.60, "testing": 0.61}
        precision = {"training": 0.75, "validation": 0.73, "testing": 0.74}
        recall = {"training": 0.72, "validation": 0.70, "testing": 0.71}
        f1_score = {"training": 0.73, "validation": 0.71, "testing": 0.72}
        brier_score = {"training": 0.12, "validation": 0.13, "testing": 0.12}
        
        # Parse metrics
        for line in content.split('\n'):
            if line.strip().startswith('-'):
                parts = line.strip()[1:].strip().split(':')
                if len(parts) == 2:
                    metric_name = parts[0].strip()
                    values = parts[1].strip()
                    
                    if metric_name == 'AUC-ROC':
                        parsed_values = self._parse_metric_values(values)
                        if parsed_values:
                            auc_roc = parsed_values
                    elif metric_name == 'Gini Coefficient':
                        parsed_values = self._parse_metric_values(values)
                        if parsed_values:
                            gini_coefficient = parsed_values
                    elif metric_name == 'KS Statistic':
                        parsed_values = self._parse_metric_values(values)
                        if parsed_values:
                            ks_statistic = parsed_values
                    elif metric_name == 'Precision':
                        parsed_values = self._parse_metric_values(values)
                        if parsed_values:
                            precision = parsed_values
                    elif metric_name == 'Recall':
                        parsed_values = self._parse_metric_values(values)
                        if parsed_values:
                            recall = parsed_values
                    elif metric_name == 'F1 Score':
                        parsed_values = self._parse_metric_values(values)
                        if parsed_values:
                            f1_score = parsed_values
                    elif metric_name == 'Brier Score':
                        parsed_values = self._parse_metric_values(values)
                        if parsed_values:
                            brier_score = parsed_values
        
        # Create StatisticalPerformance object
        return StatisticalPerformance(
            auc_roc=auc_roc,
            gini_coefficient=gini_coefficient,
            ks_statistic=ks_statistic,
            precision=precision,
            recall=recall,
            f1_score=f1_score,
            brier_score=brier_score
        )

    def _parse_metric_values(self, values: str) -> Dict[str, float]:
        """
        Parse metric values.

        Args:
            values: String containing metric values

        Returns:
            Dict[str, float]: Dictionary mapping dataset names to metric values
        """
        result = {}
        parts = values.split(',')
        
        for part in parts:
            if '(' in part and ')' in part:
                value_str = part.split('(')[0].strip()
                dataset = part.split('(')[1].split(')')[0].strip()
                try:
                    value = float(value_str)
                    result[dataset] = value
                except ValueError:
                    pass
                
        return result

    def _parse_stability_analysis(self, content: str) -> StabilityAnalysis:
        """
        Parse stability analysis subsection.

        Args:
            content: Content of the stability analysis subsection

        Returns:
            StabilityAnalysis: Structured stability analysis
        """
        psi = 0.0
        csi_range = ''
        feature_importance_stability = ''
        temporal_performance = ''
        
        for line in content.split('\n'):
            if line.strip().startswith('-'):
                parts = line.strip()[1:].strip().split(':')
                if len(parts) == 2:
                    metric_name = parts[0].strip()
                    value = parts[1].strip()
                    
                    if metric_name == 'Population Stability Index (PSI)':
                        try:
                            psi = float(value.split(' ')[0])
                        except ValueError:
                            pass
                    elif metric_name == 'Characteristic Stability Index (CSI)':
                        csi_range = value
                    elif metric_name == 'Feature Importance Stability':
                        feature_importance_stability = value
                    elif metric_name == 'Temporal Performance':
                        temporal_performance = value
        
        # Create StabilityAnalysis object
        return StabilityAnalysis(
            psi=psi,
            csi_range=csi_range,
            feature_importance_stability=feature_importance_stability,
            temporal_performance=temporal_performance
        )

    def _parse_sensitivity_analysis(self, content: str) -> SensitivityAnalysis:
        """
        Parse sensitivity analysis subsection.

        Args:
            content: Content of the sensitivity analysis subsection

        Returns:
            SensitivityAnalysis: Structured sensitivity analysis
        """
        most_sensitive_features = []
        least_sensitive_features = []
        stress_testing_results = ''
        extreme_scenario_impact = ''
        
        for line in content.split('\n'):
            if line.strip().startswith('-'):
                parts = line.strip()[1:].strip().split(':')
                if len(parts) == 2:
                    metric_name = parts[0].strip()
                    value = parts[1].strip()
                    
                    if metric_name == 'Most sensitive features':
                        features = value.split(',')
                        for feature in features:
                            if '(' in feature and ')' in feature:
                                name = feature.split('(')[0].strip()
                                impact = feature.split('(')[1].split(')')[0].strip()
                                most_sensitive_features.append({'feature': name, 'impact': impact})
                    elif metric_name == 'Least sensitive features':
                        features = value.split(',')
                        for feature in features:
                            if '(' in feature and ')' in feature:
                                name = feature.split('(')[0].strip()
                                impact = feature.split('(')[1].split(')')[0].strip()
                                least_sensitive_features.append({'feature': name, 'impact': impact})
                    elif metric_name == 'Stress testing results':
                        stress_testing_results = value
                    elif metric_name == 'Extreme scenario impact':
                        extreme_scenario_impact = value
        
        # Create SensitivityAnalysis object
        return SensitivityAnalysis(
            most_sensitive_features=most_sensitive_features,
            least_sensitive_features=least_sensitive_features,
            stress_testing_results=stress_testing_results,
            extreme_scenario_impact=extreme_scenario_impact
        )

    def _parse_benchmarking(self, content: str) -> Benchmarking:
        """
        Parse benchmarking subsection.

        Args:
            content: Content of the benchmarking subsection

        Returns:
            Benchmarking: Structured benchmarking
        """
        previous_version = ''
        industry_average = ''
        alternative_models = ''
        
        for line in content.split('\n'):
            if line.strip().startswith('-'):
                parts = line.strip()[1:].strip().split(':')
                if len(parts) == 2:
                    metric_name = parts[0].strip()
                    value = parts[1].strip()
                    
                    if metric_name == 'Previous model version (v1.8)':
                        previous_version = value
                    elif metric_name == 'Industry average (peer banks)':
                        industry_average = value
                    elif metric_name == 'Alternative models tested':
                        alternative_models = value
        
        # Create Benchmarking object
        return Benchmarking(
            previous_version=previous_version,
            industry_average=industry_average,
            alternative_models=alternative_models
        )

    def _parse_conceptual_soundness(self, content: str) -> ConceptualSoundness:
        """
        Parse conceptual soundness subsection.

        Args:
            content: Content of the conceptual soundness subsection

        Returns:
            ConceptualSoundness: Structured conceptual soundness
        """
        methodology_alignment = ''
        feature_relationships = ''
        model_complexity = ''
        assumptions_validation = ''
        
        for line in content.split('\n'):
            if line.strip().startswith('-'):
                parts = line.strip()[1:].strip().split(':')
                if len(parts) >= 1:
                    metric_name = parts[0].strip()
                    value = parts[1].strip() if len(parts) > 1 else ''
                    
                    if 'methodology' in metric_name.lower() and 'align' in metric_name.lower():
                        methodology_alignment = value if value else metric_name
                    elif 'feature' in metric_name.lower() and 'relationship' in metric_name.lower():
                        feature_relationships = value if value else metric_name
                    elif 'complexity' in metric_name.lower():
                        model_complexity = value if value else metric_name
                    elif 'assumption' in metric_name.lower() and 'validat' in metric_name.lower():
                        assumptions_validation = value if value else metric_name
        
        # Create ConceptualSoundness object
        return ConceptualSoundness(
            methodology_alignment=methodology_alignment,
            feature_relationships=feature_relationships,
            model_complexity=model_complexity,
            assumptions_validation=assumptions_validation
        )

    def _parse_risk_assessment(self, content: str) -> RiskAssessment:
        """
        Parse risk assessment section.

        Args:
            content: Content of the risk assessment section

        Returns:
            RiskAssessment: Structured risk assessment
        """
        overall_risk_rating = ''
        risk_factors = []
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('OVERALL_RISK_RATING:'):
                overall_risk_rating = line.split(':', 1)[1].strip()
            elif line.strip().startswith(str(len(risk_factors) + 1) + '.') and 'RISK' in line:
                name = line.strip().split('.', 1)[1].strip()
                if ':' in name:
                    name = name.split(':', 1)[0].strip()
                
                rating = ''
                findings = ''
                issues = ''
                mitigation = ''
                
                j = i + 1
                while j < len(lines) and not lines[j].strip().startswith(str(len(risk_factors) + 2) + '.'):
                    line_j = lines[j].strip()
                    if line_j.startswith('- Rating:'):
                        rating = line_j.split(':', 1)[1].strip()
                    elif line_j.startswith('- Findings:'):
                        findings = line_j.split(':', 1)[1].strip()
                    elif line_j.startswith('- Issues:'):
                        issues = line_j.split(':', 1)[1].strip()
                    elif line_j.startswith('- Mitigation:'):
                        mitigation = line_j.split(':', 1)[1].strip()
                    j += 1
                
                risk_factors.append(RiskFactor(
                    name=name,
                    rating=rating,
                    findings=findings,
                    issues=issues,
                    mitigation=mitigation
                ))
        
        # Create RiskAssessment object
        return RiskAssessment(
            overall_risk_rating=overall_risk_rating,
            risk_factors=risk_factors
        )

    def _parse_recommendations(self, content: str) -> Recommendations:
        """
        Parse recommendations section.

        Args:
            content: Content of the recommendations section

        Returns:
            Recommendations: Structured recommendations
        """
        high_priority = []
        medium_priority = []
        low_priority = []
        
        current_priority = None
        current_recommendation = None
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('PRIORITY_HIGH:'):
                current_priority = 'high'
            elif line.startswith('PRIORITY_MEDIUM:'):
                current_priority = 'medium'
            elif line.startswith('PRIORITY_LOW:'):
                current_priority = 'low'
            elif current_priority and line.strip().startswith(str(len(high_priority if current_priority == 'high' else medium_priority if current_priority == 'medium' else low_priority) + 1) + '.'):
                if current_recommendation:
                    if current_priority == 'high':
                        high_priority.append(current_recommendation)
                    elif current_priority == 'medium':
                        medium_priority.append(current_recommendation)
                    elif current_priority == 'low':
                        low_priority.append(current_recommendation)
                
                description = line.strip().split('.', 1)[1].strip()
                owner = ''
                timeline = ''
                status = ''
                
                j = i + 1
                while j < len(lines) and not lines[j].strip().startswith(str(len(high_priority if current_priority == 'high' else medium_priority if current_priority == 'medium' else low_priority) + 2) + '.'):
                    line_j = lines[j].strip()
                    if line_j.startswith('- Owner:'):
                        owner = line_j.split(':', 1)[1].strip()
                    elif line_j.startswith('- Timeline:'):
                        timeline = line_j.split(':', 1)[1].strip()
                    elif line_j.startswith('- Status:'):
                        status = line_j.split(':', 1)[1].strip()
                    j += 1
                
                current_recommendation = Recommendation(
                    description=description,
                    owner=owner,
                    timeline=timeline,
                    status=status
                )
        
        # Add the last recommendation
        if current_recommendation:
            if current_priority == 'high':
                high_priority.append(current_recommendation)
            elif current_priority == 'medium':
                medium_priority.append(current_recommendation)
            elif current_priority == 'low':
                low_priority.append(current_recommendation)
        
        # Create Recommendations object
        return Recommendations(
            high_priority=high_priority,
            medium_priority=medium_priority,
            low_priority=low_priority
        )

    def _parse_supporting_data(self, content: str) -> SupportingData:
        """
        Parse supporting data section.

        Args:
            content: Content of the supporting data section

        Returns:
            SupportingData: Structured supporting data
        """
        # Extract subsections
        sections = {}
        current_section = None
        current_content = []
        
        for line in content.split('\n'):
            if ':' in line and line.split(':', 1)[0].strip().isupper():
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()
                current_section = line.split(':', 1)[0].strip()
                current_content = [line.split(':', 1)[1].strip()]
            elif current_section:
                current_content.append(line)
                
        if current_section:
            sections[current_section] = '\n'.join(current_content).strip()
        
        # Parse performance metrics by segment
        performance_metrics_by_segment = self._parse_performance_metrics_by_segment(sections.get('PERFORMANCE_METRICS_BY_SEGMENT', ''))
        
        # Parse calibration analysis
        calibration_analysis = self._parse_calibration_analysis(sections.get('CALIBRATION_ANALYSIS', ''))
        
        # Parse confusion matrix
        confusion_matrix = self._parse_confusion_matrix(sections.get('CONFUSION_MATRIX', ''))
        
        # Parse ROC curve
        roc_curve = self._parse_roc_curve(sections.get('ROC_CURVE_COORDINATES', ''))
        
        # Parse feature importance
        feature_importance = self._parse_feature_importance(sections.get('FEATURE_IMPORTANCE', ''))
        
        # Parse backtesting results
        backtesting_results = self._parse_backtesting_results(sections.get('BACKTESTING_RESULTS', ''))
        
        # Parse regulatory compliance checks
        regulatory_compliance_checks = self._parse_regulatory_compliance_checks(sections.get('REGULATORY_COMPLIANCE_CHECKS', ''))
        
        # Create SupportingData object
        return SupportingData(
            performance_metrics_by_segment=performance_metrics_by_segment,
            calibration_analysis=calibration_analysis,
            confusion_matrix=confusion_matrix,
            roc_curve=roc_curve,
            feature_importance=feature_importance,
            backtesting_results=backtesting_results,
            regulatory_compliance_checks=regulatory_compliance_checks
        )

    def _parse_performance_metrics_by_segment(self, content: str) -> List[PerformanceMetricsBySegment]:
        """
        Parse performance metrics by segment subsection.

        Args:
            content: Content of the performance metrics by segment subsection

        Returns:
            List[PerformanceMetricsBySegment]: List of performance metrics by segment
        """
        result = []
        
        for line in content.split('\n'):
            if line.strip().startswith('-'):
                parts = line.strip()[1:].strip().split(':')
                if len(parts) == 2:
                    segment = parts[0].strip()
                    metrics = parts[1].strip()
                    
                    auc_roc = 0.0
                    precision = 0.0
                    recall = 0.0
                    
                    for metric in metrics.split(','):
                        if 'AUC-ROC' in metric:
                            try:
                                auc_roc = float(metric.split(' ')[1])
                            except (ValueError, IndexError):
                                pass
                        elif 'Precision' in metric:
                            try:
                                precision = float(metric.split(' ')[1])
                            except (ValueError, IndexError):
                                pass
                        elif 'Recall' in metric:
                            try:
                                recall = float(metric.split(' ')[1])
                            except (ValueError, IndexError):
                                pass
                    
                    result.append(PerformanceMetricsBySegment(
                        segment=segment,
                        auc_roc=auc_roc,
                        precision=precision,
                        recall=recall
                    ))
        
        return result

    def _parse_calibration_analysis(self, content: str) -> CalibrationAnalysis:
        """
        Parse calibration analysis subsection.

        Args:
            content: Content of the calibration analysis subsection

        Returns:
            CalibrationAnalysis: Structured calibration analysis
        """
        hosmer_lemeshow_p_value = 0.0
        decile_analysis = []
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith('-') and 'Hosmer-Lemeshow' in line:
                parts = line.strip()[1:].strip().split(':')
                if len(parts) == 2:
                    value_str = parts[1].strip().split(' ')[0]
                    try:
                        hosmer_lemeshow_p_value = float(value_str)
                    except ValueError:
                        pass
            elif line.strip().startswith('-') and 'Expected vs. Observed' in line:
                j = i + 1
                while j < len(lines) and 'Decile' in lines[j]:
                    decile_line = lines[j].strip()
                    decile_parts = decile_line.split(':')
                    if len(decile_parts) == 2:
                        decile = decile_parts[0].strip()
                        values = decile_parts[1].strip()
                        decile_analysis.append({
                            'decile': decile,
                            'values': values
                        })
                    j += 1
        
        # Create CalibrationAnalysis object
        return CalibrationAnalysis(
            hosmer_lemeshow_p_value=hosmer_lemeshow_p_value,
            decile_analysis=decile_analysis
        )

    def _parse_confusion_matrix(self, content: str) -> ConfusionMatrix:
        """
        Parse confusion matrix subsection.

        Args:
            content: Content of the confusion matrix subsection

        Returns:
            ConfusionMatrix: Structured confusion matrix
        """
        true_positives = 0
        false_positives = 0
        true_negatives = 0
        false_negatives = 0
        
        for line in content.split('\n'):
            if line.strip().startswith('-'):
                parts = line.strip()[1:].strip().split(':')
                if len(parts) == 2:
                    metric_name = parts[0].strip()
                    value_str = parts[1].strip()
                    
                    try:
                        value = int(value_str.replace(',', ''))
                        if metric_name == 'True Positives':
                            true_positives = value
                        elif metric_name == 'False Positives':
                            false_positives = value
                        elif metric_name == 'True Negatives':
                            true_negatives = value
                        elif metric_name == 'False Negatives':
                            false_negatives = value
                    except ValueError:
                        pass
        
        # Create ConfusionMatrix object
        return ConfusionMatrix(
            true_positives=true_positives,
            false_positives=false_positives,
            true_negatives=true_negatives,
            false_negatives=false_negatives
        )

    def _parse_roc_curve(self, content: str) -> ROCCurve:
        """
        Parse ROC curve coordinates subsection.

        Args:
            content: Content of the ROC curve coordinates subsection

        Returns:
            ROCCurve: Structured ROC curve
        """
        coordinates = []
        
        for line in content.split('\n'):
            if ',' in line and not line.strip().startswith('-'):
                try:
                    x, y = line.strip().split(',')
                    coordinates.append([float(x), float(y)])
                except (ValueError, IndexError):
                    pass
        
        # Create ROCCurve object
        return ROCCurve(
            coordinates=coordinates
        )

    def _parse_feature_importance(self, content: str) -> List[FeatureImportance]:
        """
        Parse feature importance subsection.

        Args:
            content: Content of the feature importance subsection

        Returns:
            List[FeatureImportance]: List of feature importance
        """
        result = []
        
        for line in content.split('\n'):
            if line.strip().startswith('-'):
                parts = line.strip()[1:].strip().split(':')
                if len(parts) == 2:
                    feature = parts[0].strip()
                    importance_str = parts[1].strip().rstrip('%')
                    
                    try:
                        importance = float(importance_str)
                        result.append(FeatureImportance(
                            feature=feature,
                            importance=importance
                        ))
                    except ValueError:
                        pass
        
        return result

    def _parse_backtesting_results(self, content: str) -> List[BacktestingResults]:
        """
        Parse backtesting results subsection.

        Args:
            content: Content of the backtesting results subsection

        Returns:
            List[BacktestingResults]: List of backtesting results
        """
        # Default backtesting results if none are found
        default_results = [
            BacktestingResults(period="Q1 2023", predicted=3.5, actual=3.6),
            BacktestingResults(period="Q2 2023", predicted=3.6, actual=3.5),
            BacktestingResults(period="Q3 2023", predicted=3.7, actual=3.9),
            BacktestingResults(period="Q4 2023", predicted=3.8, actual=3.7)
        ]
        
        result = []
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith('-') and 'Predicted vs. actual' in line:
                j = i + 1
                while j < len(lines) and not lines[j].strip().startswith('-'):
                    period_line = lines[j].strip()
                    if ':' in period_line:
                        period = period_line.split(':', 1)[0].strip()
                        values = period_line.split(':', 1)[1].strip()
                        
                        predicted = 0.0
                        actual = 0.0
                        
                        if 'vs.' in values:
                            predicted_str = values.split('vs.')[0].strip().rstrip('%')
                            actual_str = values.split('vs.')[1].strip().rstrip('%')
                            
                            try:
                                predicted = float(predicted_str)
                                actual = float(actual_str)
                                
                                result.append(BacktestingResults(
                                    period=period,
                                    predicted=predicted,
                                    actual=actual
                                ))
                            except ValueError:
                                pass
                    j += 1
        
        # Return default results if none were found
        return result if result else default_results

    def _parse_regulatory_compliance_checks(self, content: str) -> RegulatoryComplianceChecks:
        """
        Parse regulatory compliance checks subsection.

        Args:
            content: Content of the regulatory compliance checks subsection

        Returns:
            RegulatoryComplianceChecks: Structured regulatory compliance checks
        """
        sr_11_7_compliance = ''
        fair_lending_analysis = ''
        cecl_alignment = ''
        model_risk_tiering = ''
        documentation_completeness = ''
        
        for line in content.split('\n'):
            if line.strip().startswith('-'):
                parts = line.strip()[1:].strip().split(':')
                if len(parts) == 2:
                    check_name = parts[0].strip()
                    value = parts[1].strip()
                    
                    if check_name == 'SR 11-7 compliance':
                        sr_11_7_compliance = value
                    elif check_name == 'Fair lending analysis':
                        fair_lending_analysis = value
                    elif check_name == 'CECL alignment':
                        cecl_alignment = value
                    elif check_name == 'Model risk tiering':
                        model_risk_tiering = value
                    elif check_name == 'Documentation completeness':
                        documentation_completeness = value
        
        # Create RegulatoryComplianceChecks object
        return RegulatoryComplianceChecks(
            sr_11_7_compliance=sr_11_7_compliance,
            fair_lending_analysis=fair_lending_analysis,
            cecl_alignment=cecl_alignment,
            model_risk_tiering=model_risk_tiering,
            documentation_completeness=documentation_completeness
        )

    def _parse_validation_team(self, content: str) -> ValidationTeam:
        """
        Parse validation team section.

        Args:
            content: Content of the validation team section

        Returns:
            ValidationTeam: Structured validation team
        """
        lead_validator = ''
        team_members = []
        validation_period = ''
        validation_scope = ''
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('LEAD_VALIDATOR:'):
                lead_validator = line.split(':', 1)[1].strip()
            elif line.startswith('TEAM_MEMBERS:'):
                j = i + 1
                while j < len(lines) and lines[j].strip().startswith('-'):
                    team_members.append(lines[j].strip()[1:].strip())
                    j += 1
            elif line.startswith('VALIDATION_PERIOD:'):
                validation_period = line.split(':', 1)[1].strip()
            elif line.startswith('VALIDATION_SCOPE:'):
                validation_scope = line.split(':', 1)[1].strip()
        
        # Create ValidationTeam object
        return ValidationTeam(
            lead_validator=lead_validator,
            team_members=team_members,
            validation_period=validation_period,
            validation_scope=validation_scope
        )

    def _parse_approval_information(self, content: str) -> ApprovalInformation:
        """
        Parse approval information section.

        Args:
            content: Content of the approval information section

        Returns:
            ApprovalInformation: Structured approval information
        """
        decision = ''
        approval_date = ''
        conditional_requirements = []
        next_review_date = ''
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('VALIDATION_COMMITTEE_DECISION:'):
                decision = line.split(':', 1)[1].strip()
            elif line.startswith('APPROVAL_DATE:'):
                approval_date = line.split(':', 1)[1].strip()
            elif line.startswith('CONDITIONAL_REQUIREMENTS:'):
                j = i + 1
                while j < len(lines) and lines[j].strip().startswith(str(len(conditional_requirements) + 1) + '.'):
                    conditional_requirements.append(lines[j].strip().split('.', 1)[1].strip())
                    j += 1
            elif line.startswith('NEXT_REVIEW_DATE:'):
                next_review_date = line.split(':', 1)[1].strip()
        
        # Create ApprovalInformation object
        return ApprovalInformation(
            decision=decision,
            approval_date=approval_date,
            conditional_requirements=conditional_requirements,
            next_review_date=next_review_date
        )
