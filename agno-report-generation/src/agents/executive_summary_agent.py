"""
Executive Summary Agent for the MRM Report Generation System.

This agent is responsible for generating a concise and informative executive summary
for the report, highlighting key findings, risks, and recommendations.
"""

import os
from typing import Dict, Any, List

from agno import Agent, Message, AgentConfig
from agno.tools import Tool, ToolConfig


class ExecutiveSummaryAgent:
    """
    Executive Summary Agent for the MRM Report Generation System.
    
    This agent generates a concise and informative executive summary for the report by:
    1. Extracting key findings from the validation results
    2. Summarizing risk assessment findings
    3. Highlighting critical recommendations
    4. Providing a high-level overview of model performance
    """
    
    def __init__(self, api_key: str, model: str = "claude-3-opus-20240229"):
        """
        Initialize the Executive Summary Agent.
        
        Args:
            api_key: Anthropic API key
            model: Model to use for the agent
        """
        self.api_key = api_key
        self.model = model
        
        # Create the agent
        self.agent = Agent(
            config=AgentConfig(
                name="Executive Summary Agent",
                description="Generates concise and informative executive summaries for model validation reports",
                model=self.model,
                api_key=self.api_key
            )
        )
    
    def generate_executive_summary(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate an executive summary for the report.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: Executive summary content
        """
        # Extract relevant information from the parsed data
        model_metadata = parsed_data.get("model_metadata", {})
        model_methodology = parsed_data.get("model_methodology", {})
        validation_tests = parsed_data.get("validation_tests", {})
        risk_assessment = parsed_data.get("risk_assessment", {})
        recommendations = parsed_data.get("recommendations", {})
        
        # Prepare the prompt for the agent
        prompt = f"""
        Generate a concise and informative executive summary for a model validation report with the following information:
        
        Model Information:
        - Name: {model_metadata.get("name", "")}
        - Version: {model_metadata.get("version", "")}
        - Type: {model_methodology.get("model_type", "")}
        - Purpose: {model_methodology.get("description", "")}
        
        Validation Results:
        - Statistical Performance: {validation_tests.get("statistical_performance", {})}
        - Stability Analysis: {validation_tests.get("stability_analysis", {})}
        - Sensitivity Analysis: {validation_tests.get("sensitivity_analysis", {})}
        - Benchmarking: {validation_tests.get("benchmarking", {})}
        - Conceptual Soundness: {validation_tests.get("conceptual_soundness", {})}
        
        Risk Assessment:
        - Overall Risk Rating: {risk_assessment.get("overall_risk_rating", "")}
        - Risk Factors: {risk_assessment.get("risk_factors", [])}
        
        Recommendations:
        - High Priority: {recommendations.get("high_priority", [])}
        - Medium Priority: {recommendations.get("medium_priority", [])}
        - Low Priority: {recommendations.get("low_priority", [])}
        
        The executive summary should include:
        1. A brief overview of the model and its purpose
        2. Key validation findings and performance metrics
        3. Overall risk assessment and key risk factors
        4. Critical recommendations
        5. Conclusion with overall assessment of model readiness
        
        The executive summary should be written for senior executives and should be no more than 2-3 pages when printed.
        Use clear, concise language and avoid technical jargon where possible.
        Structure the summary with appropriate headings and bullet points for readability.
        """
        
        # Get the executive summary from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        executive_summary = self._parse_executive_summary(response.content)
        
        return executive_summary
    
    def _parse_executive_summary(self, response: str) -> Dict[str, Any]:
        """
        Parse the executive summary response from the agent into structured content.
        
        Args:
            response: Response from the agent
            
        Returns:
            Dict[str, Any]: Structured executive summary content
        """
        # This is a simplified parser that assumes the response is in a specific format
        # In a real implementation, this would be more robust
        sections = {}
        current_section = None
        current_content = []
        
        # Split the response into lines
        lines = response.strip().split("\n")
        
        for line in lines:
            line = line.strip()
            
            # Skip empty lines
            if not line:
                continue
            
            # Check if this is a heading (assuming headings are in title case and don't end with punctuation)
            if line.istitle() and not line.endswith((".",":",";","?","!")):
                # Save the previous section
                if current_section is not None:
                    sections[current_section] = "\n".join(current_content)
                
                # Start a new section
                current_section = line
                current_content = []
            else:
                # Add to the current section
                if current_section is not None:
                    current_content.append(line)
        
        # Save the last section
        if current_section is not None:
            sections[current_section] = "\n".join(current_content)
        
        # Structure the executive summary
        executive_summary = {
            "overview": sections.get("Overview", sections.get("Introduction", "")),
            "key_findings": sections.get("Key Findings", sections.get("Validation Results", "")),
            "risk_assessment": sections.get("Risk Assessment", ""),
            "recommendations": sections.get("Recommendations", sections.get("Key Recommendations", "")),
            "conclusion": sections.get("Conclusion", ""),
            "full_text": response
        }
        
        return executive_summary
