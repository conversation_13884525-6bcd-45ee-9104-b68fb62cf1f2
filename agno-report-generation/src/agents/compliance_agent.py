"""
Compliance Agent for the MRM Report Generation System.

This agent is responsible for ensuring that the report complies with regulatory requirements,
including SR 11-7, CECL, and fair lending regulations.
"""

import os
from typing import Dict, Any, List

from agno import Agent, Message, AgentConfig
from agno.tools import Tool, ToolConfig


class ComplianceAgent:
    """
    Compliance Agent for the MRM Report Generation System.
    
    This agent ensures that the report complies with regulatory requirements by:
    1. Checking for compliance with SR 11-7 requirements
    2. Verifying CECL compliance
    3. Ensuring fair lending compliance
    4. Validating that all required sections are present and complete
    """
    
    def __init__(self, api_key: str, model: str = "claude-3-opus-20240229"):
        """
        Initialize the Compliance Agent.
        
        Args:
            api_key: Anthropic API key
            model: Model to use for the agent
        """
        self.api_key = api_key
        self.model = model
        
        # Create the agent
        self.agent = Agent(
            config=AgentConfig(
                name="Compliance Agent",
                description="Ensures that model validation reports comply with regulatory requirements",
                model=self.model,
                api_key=self.api_key
            )
        )
    
    def check_compliance(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check the report for compliance with regulatory requirements.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: Compliance assessment results
        """
        # Check for SR 11-7 compliance
        sr_11_7_compliance = self._check_sr_11_7_compliance(parsed_data)
        
        # Check for CECL compliance
        cecl_compliance = self._check_cecl_compliance(parsed_data)
        
        # Check for fair lending compliance
        fair_lending_compliance = self._check_fair_lending_compliance(parsed_data)
        
        # Check for completeness
        completeness = self._check_completeness(parsed_data)
        
        # Combine all compliance checks
        compliance_results = {
            "sr_11_7_compliance": sr_11_7_compliance,
            "cecl_compliance": cecl_compliance,
            "fair_lending_compliance": fair_lending_compliance,
            "completeness": completeness,
            "overall_compliance": self._assess_overall_compliance(
                sr_11_7_compliance,
                cecl_compliance,
                fair_lending_compliance,
                completeness
            )
        }
        
        return compliance_results
    
    def _check_sr_11_7_compliance(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check for compliance with SR 11-7 requirements.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: SR 11-7 compliance assessment
        """
        # Extract relevant information from the parsed data
        model_metadata = parsed_data.get("model_metadata", {})
        model_methodology = parsed_data.get("model_methodology", {})
        validation_tests = parsed_data.get("validation_tests", {})
        risk_assessment = parsed_data.get("risk_assessment", {})
        recommendations = parsed_data.get("recommendations", {})
        
        # Prepare the prompt for the agent
        prompt = f"""
        Assess the compliance of the following model validation report with SR 11-7 requirements:
        
        Model Information:
        - Name: {model_metadata.get("name", "")}
        - Version: {model_metadata.get("version", "")}
        - Type: {model_methodology.get("model_type", "")}
        - Purpose: {model_methodology.get("description", "")}
        
        Validation Tests:
        - Statistical Performance: {validation_tests.get("statistical_performance", {})}
        - Stability Analysis: {validation_tests.get("stability_analysis", {})}
        - Sensitivity Analysis: {validation_tests.get("sensitivity_analysis", {})}
        - Benchmarking: {validation_tests.get("benchmarking", {})}
        - Conceptual Soundness: {validation_tests.get("conceptual_soundness", {})}
        
        Risk Assessment:
        - Overall Risk Rating: {risk_assessment.get("overall_risk_rating", "")}
        - Risk Factors: {risk_assessment.get("risk_factors", [])}
        
        Recommendations:
        - High Priority: {recommendations.get("high_priority", [])}
        - Medium Priority: {recommendations.get("medium_priority", [])}
        - Low Priority: {recommendations.get("low_priority", [])}
        
        SR 11-7 requires model validation to include:
        1. Evaluation of conceptual soundness
        2. Ongoing monitoring
        3. Outcomes analysis
        4. Effective challenge
        5. Comprehensive documentation
        
        For each requirement, assess whether the report is:
        - Fully Compliant: All aspects of the requirement are addressed
        - Partially Compliant: Some aspects of the requirement are addressed, but there are gaps
        - Non-Compliant: The requirement is not adequately addressed
        
        For each requirement, provide specific evidence from the report to support your assessment.
        Also identify any gaps or areas for improvement.
        
        Provide an overall assessment of SR 11-7 compliance based on these individual assessments.
        """
        
        # Get the compliance assessment from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        sr_11_7_assessment = self._parse_compliance_response(response.content)
        
        return sr_11_7_assessment
    
    def _check_cecl_compliance(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check for compliance with CECL requirements.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: CECL compliance assessment
        """
        # Extract relevant information from the parsed data
        model_metadata = parsed_data.get("model_metadata", {})
        model_methodology = parsed_data.get("model_methodology", {})
        validation_tests = parsed_data.get("validation_tests", {})
        
        # Prepare the prompt for the agent
        prompt = f"""
        Assess the compliance of the following model validation report with CECL (Current Expected Credit Loss) requirements:
        
        Model Information:
        - Name: {model_metadata.get("name", "")}
        - Version: {model_metadata.get("version", "")}
        - Type: {model_methodology.get("model_type", "")}
        - Purpose: {model_methodology.get("description", "")}
        
        Validation Tests:
        - Statistical Performance: {validation_tests.get("statistical_performance", {})}
        - Stability Analysis: {validation_tests.get("stability_analysis", {})}
        - Sensitivity Analysis: {validation_tests.get("sensitivity_analysis", {})}
        
        CECL requires:
        1. Estimation of expected credit losses over the life of a loan
        2. Incorporation of reasonable and supportable forecasts
        3. Consideration of historical loss experience
        4. Appropriate segmentation of the portfolio
        
        For each requirement, assess whether the report is:
        - Fully Compliant: All aspects of the requirement are addressed
        - Partially Compliant: Some aspects of the requirement are addressed, but there are gaps
        - Non-Compliant: The requirement is not adequately addressed
        - Not Applicable: The requirement does not apply to this model
        
        For each requirement, provide specific evidence from the report to support your assessment.
        Also identify any gaps or areas for improvement.
        
        Provide an overall assessment of CECL compliance based on these individual assessments.
        """
        
        # Get the compliance assessment from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        cecl_assessment = self._parse_compliance_response(response.content)
        
        return cecl_assessment
    
    def _check_fair_lending_compliance(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check for compliance with fair lending requirements.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: Fair lending compliance assessment
        """
        # Extract relevant information from the parsed data
        model_metadata = parsed_data.get("model_metadata", {})
        model_methodology = parsed_data.get("model_methodology", {})
        validation_tests = parsed_data.get("validation_tests", {})
        
        # Prepare the prompt for the agent
        prompt = f"""
        Assess the compliance of the following model validation report with fair lending requirements:
        
        Model Information:
        - Name: {model_metadata.get("name", "")}
        - Version: {model_metadata.get("version", "")}
        - Type: {model_methodology.get("model_type", "")}
        - Purpose: {model_methodology.get("description", "")}
        - Features: {model_methodology.get("feature_selection", "")}
        
        Validation Tests:
        - Statistical Performance: {validation_tests.get("statistical_performance", {})}
        - Segment Performance: {validation_tests.get("statistical_performance", {}).get("segment_performance", {})}
        
        Fair lending requirements include:
        1. No disparate impact on protected classes
        2. No use of prohibited bases as model features
        3. Consistent model performance across demographic groups
        4. Appropriate monitoring for potential bias
        
        For each requirement, assess whether the report is:
        - Fully Compliant: All aspects of the requirement are addressed
        - Partially Compliant: Some aspects of the requirement are addressed, but there are gaps
        - Non-Compliant: The requirement is not adequately addressed
        - Not Applicable: The requirement does not apply to this model
        
        For each requirement, provide specific evidence from the report to support your assessment.
        Also identify any gaps or areas for improvement.
        
        Provide an overall assessment of fair lending compliance based on these individual assessments.
        """
        
        # Get the compliance assessment from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        fair_lending_assessment = self._parse_compliance_response(response.content)
        
        return fair_lending_assessment
    
    def _check_completeness(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check for completeness of the report.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, Any]: Completeness assessment
        """
        # Define the required sections for a complete report
        required_sections = [
            "model_metadata",
            "model_methodology",
            "validation_tests",
            "risk_assessment",
            "recommendations",
            "validation_team",
            "approval_information"
        ]
        
        # Check if each required section is present and non-empty
        missing_sections = []
        incomplete_sections = []
        
        for section in required_sections:
            if section not in parsed_data:
                missing_sections.append(section)
            elif not parsed_data[section]:
                incomplete_sections.append(section)
        
        # Prepare the prompt for the agent
        prompt = f"""
        Assess the completeness of the following model validation report:
        
        Missing Sections: {missing_sections}
        Incomplete Sections: {incomplete_sections}
        
        A complete model validation report should include:
        1. Model metadata (name, version, type, etc.)
        2. Model methodology (description, feature selection, training approach, etc.)
        3. Validation tests (statistical performance, stability, sensitivity, benchmarking, conceptual soundness)
        4. Risk assessment (overall rating, risk factors)
        5. Recommendations (high, medium, low priority)
        6. Validation team information
        7. Approval information
        
        For each section, assess whether the report is:
        - Complete: All required information is present
        - Partially Complete: Some required information is present, but there are gaps
        - Incomplete: The section is missing or severely lacking in required information
        
        Provide an overall assessment of completeness based on these individual assessments.
        """
        
        # Get the completeness assessment from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        completeness_assessment = self._parse_compliance_response(response.content)
        
        return completeness_assessment
    
    def _assess_overall_compliance(
        self,
        sr_11_7_compliance: Dict[str, Any],
        cecl_compliance: Dict[str, Any],
        fair_lending_compliance: Dict[str, Any],
        completeness: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Assess overall compliance based on individual compliance assessments.
        
        Args:
            sr_11_7_compliance: SR 11-7 compliance assessment
            cecl_compliance: CECL compliance assessment
            fair_lending_compliance: Fair lending compliance assessment
            completeness: Completeness assessment
            
        Returns:
            Dict[str, Any]: Overall compliance assessment
        """
        # Extract overall assessments from each compliance check
        sr_11_7_overall = sr_11_7_compliance.get("overall_assessment", "")
        cecl_overall = cecl_compliance.get("overall_assessment", "")
        fair_lending_overall = fair_lending_compliance.get("overall_assessment", "")
        completeness_overall = completeness.get("overall_assessment", "")
        
        # Prepare the prompt for the agent
        prompt = f"""
        Assess the overall regulatory compliance of a model validation report based on the following individual assessments:
        
        SR 11-7 Compliance: {sr_11_7_overall}
        CECL Compliance: {cecl_overall}
        Fair Lending Compliance: {fair_lending_overall}
        Completeness: {completeness_overall}
        
        Provide an overall compliance assessment that considers all of these individual assessments.
        The overall assessment should be one of:
        - Fully Compliant: The report meets all regulatory requirements
        - Substantially Compliant: The report meets most regulatory requirements, with minor gaps
        - Partially Compliant: The report meets some regulatory requirements, but has significant gaps
        - Non-Compliant: The report fails to meet critical regulatory requirements
        
        Provide a detailed explanation for your overall assessment, highlighting the key strengths and weaknesses of the report from a regulatory compliance perspective.
        """
        
        # Get the overall compliance assessment from the agent
        response = self.agent.chat([Message.user(prompt)])
        
        # Parse the response into structured content
        overall_assessment = {
            "assessment": self._extract_overall_rating(response.content),
            "explanation": response.content
        }
        
        return overall_assessment
    
    def _parse_compliance_response(self, response: str) -> Dict[str, Any]:
        """
        Parse the compliance response from the agent into structured content.
        
        Args:
            response: Response from the agent
            
        Returns:
            Dict[str, Any]: Structured compliance assessment
        """
        # This is a simplified parser that assumes the response is in a specific format
        # In a real implementation, this would be more robust
        sections = {}
        current_section = None
        current_content = []
        
        # Split the response into lines
        lines = response.strip().split("\n")
        
        for line in lines:
            line = line.strip()
            
            # Skip empty lines
            if not line:
                continue
            
            # Check if this is a heading (assuming headings end with a colon)
            if line.endswith(":"):
                # Save the previous section
                if current_section is not None:
                    sections[current_section] = "\n".join(current_content)
                
                # Start a new section
                current_section = line[:-1]  # Remove the colon
                current_content = []
            else:
                # Add to the current section
                if current_section is not None:
                    current_content.append(line)
        
        # Save the last section
        if current_section is not None:
            sections[current_section] = "\n".join(current_content)
        
        # Extract the overall assessment
        overall_assessment = sections.get("Overall Assessment", "")
        
        # Structure the compliance assessment
        compliance_assessment = {
            "requirements": {},
            "overall_assessment": overall_assessment,
            "full_text": response
        }
        
        # Extract individual requirement assessments
        for section, content in sections.items():
            if section != "Overall Assessment" and section != "Summary":
                compliance_assessment["requirements"][section] = {
                    "assessment": self._extract_compliance_rating(content),
                    "evidence": content
                }
        
        return compliance_assessment
    
    def _extract_compliance_rating(self, text: str) -> str:
        """
        Extract the compliance rating from the text.
        
        Args:
            text: Text to extract the rating from
            
        Returns:
            str: Compliance rating
        """
        # Look for common compliance ratings in the text
        if "fully compliant" in text.lower():
            return "Fully Compliant"
        elif "substantially compliant" in text.lower():
            return "Substantially Compliant"
        elif "partially compliant" in text.lower():
            return "Partially Compliant"
        elif "non-compliant" in text.lower() or "noncompliant" in text.lower():
            return "Non-Compliant"
        elif "not applicable" in text.lower():
            return "Not Applicable"
        else:
            return "Unknown"
    
    def _extract_overall_rating(self, text: str) -> str:
        """
        Extract the overall compliance rating from the text.
        
        Args:
            text: Text to extract the rating from
            
        Returns:
            str: Overall compliance rating
        """
        # Look for common overall ratings in the text
        if "fully compliant" in text.lower():
            return "Fully Compliant"
        elif "substantially compliant" in text.lower():
            return "Substantially Compliant"
        elif "partially compliant" in text.lower():
            return "Partially Compliant"
        elif "non-compliant" in text.lower() or "noncompliant" in text.lower():
            return "Non-Compliant"
        else:
            return "Unknown"
