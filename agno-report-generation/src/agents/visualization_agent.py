"""
Visualization Agent for the MRM Report Generation System.

This agent is responsible for generating charts and visualizations for the report,
including performance metrics, risk assessments, and data distributions.
"""

import os
import base64
from typing import Dict, Any, List, Tuple
import json
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from io import BytesIO

from agno import Agent, Message, AgentConfig
from agno.tools import Tool, ToolConfig


class VisualizationAgent:
    """
    Visualization Agent for the MRM Report Generation System.
    
    This agent generates charts and visualizations for the report by:
    1. Creating performance metric visualizations
    2. Generating risk assessment visualizations
    3. Visualizing data distributions and feature importance
    4. Creating comparison charts for benchmarking
    """
    
    def __init__(self, api_key: str, model: str = "claude-3-opus-20240229"):
        """
        Initialize the Visualization Agent.
        
        Args:
            api_key: Anthropic API key
            model: Model to use for the agent
        """
        self.api_key = api_key
        self.model = model
        self.output_dir = "charts"
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Set the style for the charts
        sns.set_style("whitegrid")
        plt.rcParams['figure.figsize'] = (10, 6)
        plt.rcParams['font.size'] = 12
        
        # Create the agent
        self.agent = Agent(
            config=AgentConfig(
                name="Visualization Agent",
                description="Generates charts and visualizations for model validation reports",
                model=self.model,
                api_key=self.api_key
            )
        )
    
    def generate_visualizations(self, parsed_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate visualizations for the report.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, str]: Dictionary mapping chart names to file paths
        """
        charts = {}
        
        # Generate performance metric charts
        performance_charts = self._generate_performance_charts(parsed_data)
        charts.update(performance_charts)
        
        # Generate risk assessment charts
        risk_charts = self._generate_risk_charts(parsed_data)
        charts.update(risk_charts)
        
        # Generate feature importance charts
        feature_charts = self._generate_feature_charts(parsed_data)
        charts.update(feature_charts)
        
        # Generate comparison charts
        comparison_charts = self._generate_comparison_charts(parsed_data)
        charts.update(comparison_charts)
        
        return charts
    
    def _generate_performance_charts(self, parsed_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate performance metric charts.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, str]: Dictionary mapping chart names to file paths
        """
        charts = {}
        
        # Extract performance metrics
        validation_tests = parsed_data.get("validation_tests", {})
        statistical_performance = validation_tests.get("statistical_performance", {})
        
        # Generate ROC curve
        if "auc_roc" in statistical_performance:
            roc_curve_path = os.path.join(self.output_dir, "roc_curve.png")
            self._generate_roc_curve(
                auc_roc=statistical_performance.get("auc_roc", 0.5),
                output_path=roc_curve_path
            )
            charts["roc_curve"] = roc_curve_path
        
        # Generate performance by segment
        if "segment_performance" in statistical_performance:
            segment_performance_path = os.path.join(self.output_dir, "performance_by_segment.png")
            self._generate_segment_performance_chart(
                segment_performance=statistical_performance.get("segment_performance", {}),
                output_path=segment_performance_path
            )
            charts["performance_by_segment"] = segment_performance_path
        
        # Generate calibration curve
        if "calibration" in statistical_performance:
            calibration_path = os.path.join(self.output_dir, "calibration.png")
            self._generate_calibration_curve(
                calibration=statistical_performance.get("calibration", {}),
                output_path=calibration_path
            )
            charts["calibration"] = calibration_path
        
        # Generate backtesting chart
        if "backtesting" in validation_tests:
            backtesting_path = os.path.join(self.output_dir, "backtesting.png")
            self._generate_backtesting_chart(
                backtesting=validation_tests.get("backtesting", {}),
                output_path=backtesting_path
            )
            charts["backtesting"] = backtesting_path
        
        return charts
    
    def _generate_risk_charts(self, parsed_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate risk assessment charts.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, str]: Dictionary mapping chart names to file paths
        """
        charts = {}
        
        # Extract risk assessment
        risk_assessment = parsed_data.get("risk_assessment", {})
        
        # Generate risk heatmap
        risk_heatmap_path = os.path.join(self.output_dir, "risk_heatmap.png")
        self._generate_risk_heatmap(
            risk_factors=risk_assessment.get("risk_factors", []),
            output_path=risk_heatmap_path
        )
        charts["risk_heatmap"] = risk_heatmap_path
        
        # Generate risk radar chart
        risk_radar_path = os.path.join(self.output_dir, "risk_radar.png")
        self._generate_risk_radar(
            risk_factors=risk_assessment.get("risk_factors", []),
            output_path=risk_radar_path
        )
        charts["risk_radar"] = risk_radar_path
        
        return charts
    
    def _generate_feature_charts(self, parsed_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate feature importance and distribution charts.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, str]: Dictionary mapping chart names to file paths
        """
        charts = {}
        
        # Extract feature information
        validation_tests = parsed_data.get("validation_tests", {})
        
        # Generate feature importance chart
        if "feature_importance" in validation_tests:
            feature_importance_path = os.path.join(self.output_dir, "feature_importance.png")
            self._generate_feature_importance_chart(
                feature_importance=validation_tests.get("feature_importance", {}),
                output_path=feature_importance_path
            )
            charts["feature_importance"] = feature_importance_path
        
        # Generate feature distributions chart
        if "feature_distributions" in validation_tests:
            feature_distributions_path = os.path.join(self.output_dir, "feature_distributions.png")
            self._generate_feature_distributions_chart(
                feature_distributions=validation_tests.get("feature_distributions", {}),
                output_path=feature_distributions_path
            )
            charts["feature_distributions"] = feature_distributions_path
        
        return charts
    
    def _generate_comparison_charts(self, parsed_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate comparison charts for benchmarking.
        
        Args:
            parsed_data: Parsed data from the input file
            
        Returns:
            Dict[str, str]: Dictionary mapping chart names to file paths
        """
        charts = {}
        
        # Extract benchmarking information
        validation_tests = parsed_data.get("validation_tests", {})
        benchmarking = validation_tests.get("benchmarking", {})
        
        # Generate benchmark comparison chart
        if benchmarking:
            benchmark_comparison_path = os.path.join(self.output_dir, "benchmark_comparison.png")
            self._generate_benchmark_comparison_chart(
                benchmarking=benchmarking,
                output_path=benchmark_comparison_path
            )
            charts["benchmark_comparison"] = benchmark_comparison_path
        
        return charts
    
    def _generate_roc_curve(self, auc_roc: float, output_path: str) -> None:
        """
        Generate a ROC curve chart.
        
        Args:
            auc_roc: Area under the ROC curve
            output_path: Path to save the chart
        """
        # Generate a sample ROC curve based on the AUC value
        # In a real implementation, this would use actual false positive and true positive rates
        fpr = np.linspace(0, 1, 100)
        tpr = np.power(fpr, (1 / auc_roc - 1))
        
        plt.figure(figsize=(8, 8))
        plt.plot(fpr, tpr, 'b-', linewidth=2, label=f'Model (AUC = {auc_roc:.2f})')
        plt.plot([0, 1], [0, 1], 'r--', linewidth=2, label='Random')
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('Receiver Operating Characteristic (ROC) Curve')
        plt.legend(loc='lower right')
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_segment_performance_chart(self, segment_performance: Dict[str, Any], output_path: str) -> None:
        """
        Generate a segment performance chart.
        
        Args:
            segment_performance: Segment performance data
            output_path: Path to save the chart
        """
        # In a real implementation, this would use actual segment performance data
        # For now, we'll generate sample data
        segments = ['Segment A', 'Segment B', 'Segment C', 'Segment D', 'Segment E']
        metrics = ['AUC', 'Precision', 'Recall', 'F1 Score']
        
        # Generate random performance metrics for each segment
        np.random.seed(42)  # For reproducibility
        data = np.random.uniform(0.7, 0.9, size=(len(segments), len(metrics)))
        
        # Create a DataFrame for easier plotting
        df = pd.DataFrame(data, index=segments, columns=metrics)
        
        # Plot the segment performance
        plt.figure(figsize=(12, 8))
        ax = df.plot(kind='bar', width=0.8)
        plt.xlabel('Segment')
        plt.ylabel('Performance Metric')
        plt.title('Model Performance by Segment')
        plt.legend(title='Metric')
        plt.grid(True, axis='y')
        plt.ylim(0, 1)
        
        # Add value labels on the bars
        for container in ax.containers:
            ax.bar_label(container, fmt='%.2f', padding=3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_calibration_curve(self, calibration: Dict[str, Any], output_path: str) -> None:
        """
        Generate a calibration curve chart.
        
        Args:
            calibration: Calibration data
            output_path: Path to save the chart
        """
        # In a real implementation, this would use actual calibration data
        # For now, we'll generate sample data
        pred_probs = np.linspace(0, 1, 10)
        observed_freqs = np.clip(pred_probs + np.random.normal(0, 0.05, size=10), 0, 1)
        
        plt.figure(figsize=(8, 8))
        plt.plot(pred_probs, observed_freqs, 'bo-', linewidth=2, label='Model')
        plt.plot([0, 1], [0, 1], 'r--', linewidth=2, label='Perfect Calibration')
        plt.xlabel('Predicted Probability')
        plt.ylabel('Observed Frequency')
        plt.title('Calibration Curve')
        plt.legend(loc='lower right')
        plt.grid(True)
        plt.xlim(0, 1)
        plt.ylim(0, 1)
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_backtesting_chart(self, backtesting: Dict[str, Any], output_path: str) -> None:
        """
        Generate a backtesting chart.
        
        Args:
            backtesting: Backtesting data
            output_path: Path to save the chart
        """
        # In a real implementation, this would use actual backtesting data
        # For now, we'll generate sample data
        periods = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        predicted = np.random.normal(0.05, 0.01, size=12)
        actual = predicted + np.random.normal(0, 0.015, size=12)
        
        plt.figure(figsize=(12, 6))
        plt.plot(periods, predicted, 'bo-', linewidth=2, label='Predicted')
        plt.plot(periods, actual, 'ro-', linewidth=2, label='Actual')
        plt.xlabel('Time Period')
        plt.ylabel('Default Rate')
        plt.title('Backtesting Results: Predicted vs. Actual Default Rates')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_risk_heatmap(self, risk_factors: List[Dict[str, Any]], output_path: str) -> None:
        """
        Generate a risk heatmap.
        
        Args:
            risk_factors: List of risk factors
            output_path: Path to save the chart
        """
        # In a real implementation, this would use actual risk factor data
        # For now, we'll generate sample data
        risk_categories = ['Data Quality', 'Model Complexity', 'Implementation', 'Governance', 'Compliance']
        risk_areas = ['Area 1', 'Area 2', 'Area 3', 'Area 4', 'Area 5']
        
        # Generate random risk scores (1-5, where 5 is highest risk)
        np.random.seed(42)  # For reproducibility
        risk_scores = np.random.randint(1, 6, size=(len(risk_categories), len(risk_areas)))
        
        # Create a DataFrame for easier plotting
        df = pd.DataFrame(risk_scores, index=risk_categories, columns=risk_areas)
        
        # Plot the risk heatmap
        plt.figure(figsize=(10, 8))
        sns.heatmap(df, annot=True, cmap='RdYlGn_r', linewidths=0.5, fmt='d', cbar_kws={'label': 'Risk Score (1-5)'})
        plt.title('Risk Assessment Heatmap')
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_risk_radar(self, risk_factors: List[Dict[str, Any]], output_path: str) -> None:
        """
        Generate a risk radar chart.
        
        Args:
            risk_factors: List of risk factors
            output_path: Path to save the chart
        """
        # In a real implementation, this would use actual risk factor data
        # For now, we'll generate sample data
        categories = ['Data Quality', 'Model Complexity', 'Implementation', 'Governance', 'Compliance', 'Documentation']
        
        # Generate random risk scores (1-5, where 5 is highest risk)
        np.random.seed(42)  # For reproducibility
        values = np.random.randint(1, 6, size=len(categories))
        
        # Number of variables
        N = len(categories)
        
        # What will be the angle of each axis in the plot (divide the plot / number of variables)
        angles = [n / float(N) * 2 * np.pi for n in range(N)]
        angles += angles[:1]  # Close the loop
        
        # Values for the plot, with the first value repeated to close the loop
        values = np.append(values, values[0])
        
        # Create the plot
        plt.figure(figsize=(8, 8))
        ax = plt.subplot(111, polar=True)
        
        # Draw one axis per variable + add labels
        plt.xticks(angles[:-1], categories, size=12)
        
        # Draw the risk level circles and labels
        ax.set_rlabel_position(0)
        plt.yticks([1, 2, 3, 4, 5], ['1', '2', '3', '4', '5'], color="grey", size=10)
        plt.ylim(0, 5)
        
        # Plot the risk values
        ax.plot(angles, values, linewidth=2, linestyle='solid')
        ax.fill(angles, values, alpha=0.4)
        
        plt.title('Risk Assessment Radar Chart', size=15, y=1.1)
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_feature_importance_chart(self, feature_importance: Dict[str, Any], output_path: str) -> None:
        """
        Generate a feature importance chart.
        
        Args:
            feature_importance: Feature importance data
            output_path: Path to save the chart
        """
        # In a real implementation, this would use actual feature importance data
        # For now, we'll generate sample data
        features = ['Feature A', 'Feature B', 'Feature C', 'Feature D', 'Feature E', 
                   'Feature F', 'Feature G', 'Feature H', 'Feature I', 'Feature J']
        
        # Generate random importance scores
        np.random.seed(42)  # For reproducibility
        importance = np.random.uniform(0, 1, size=len(features))
        importance = importance / importance.sum()  # Normalize to sum to 1
        
        # Sort by importance
        sorted_idx = np.argsort(importance)
        sorted_features = [features[i] for i in sorted_idx]
        sorted_importance = importance[sorted_idx]
        
        # Plot the feature importance
        plt.figure(figsize=(10, 8))
        plt.barh(sorted_features, sorted_importance, color='skyblue')
        plt.xlabel('Relative Importance')
        plt.ylabel('Feature')
        plt.title('Feature Importance')
        plt.grid(True, axis='x')
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_feature_distributions_chart(self, feature_distributions: Dict[str, Any], output_path: str) -> None:
        """
        Generate feature distributions charts.
        
        Args:
            feature_distributions: Feature distributions data
            output_path: Path to save the chart
        """
        # In a real implementation, this would use actual feature distribution data
        # For now, we'll generate sample data for a few key features
        np.random.seed(42)  # For reproducibility
        
        # Create a figure with subplots
        fig, axs = plt.subplots(2, 2, figsize=(12, 10))
        
        # Feature 1: Normal distribution
        data1 = np.random.normal(0, 1, 1000)
        axs[0, 0].hist(data1, bins=30, alpha=0.7, color='skyblue')
        axs[0, 0].set_title('Feature A Distribution')
        axs[0, 0].set_xlabel('Value')
        axs[0, 0].set_ylabel('Frequency')
        axs[0, 0].grid(True, alpha=0.3)
        
        # Feature 2: Skewed distribution
        data2 = np.random.exponential(2, 1000)
        axs[0, 1].hist(data2, bins=30, alpha=0.7, color='lightgreen')
        axs[0, 1].set_title('Feature B Distribution')
        axs[0, 1].set_xlabel('Value')
        axs[0, 1].set_ylabel('Frequency')
        axs[0, 1].grid(True, alpha=0.3)
        
        # Feature 3: Bimodal distribution
        data3 = np.concatenate([np.random.normal(-2, 0.5, 500), np.random.normal(2, 0.5, 500)])
        axs[1, 0].hist(data3, bins=30, alpha=0.7, color='salmon')
        axs[1, 0].set_title('Feature C Distribution')
        axs[1, 0].set_xlabel('Value')
        axs[1, 0].set_ylabel('Frequency')
        axs[1, 0].grid(True, alpha=0.3)
        
        # Feature 4: Uniform distribution
        data4 = np.random.uniform(-3, 3, 1000)
        axs[1, 1].hist(data4, bins=30, alpha=0.7, color='mediumpurple')
        axs[1, 1].set_title('Feature D Distribution')
        axs[1, 1].set_xlabel('Value')
        axs[1, 1].set_ylabel('Frequency')
        axs[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_benchmark_comparison_chart(self, benchmarking: Dict[str, Any], output_path: str) -> None:
        """
        Generate a benchmark comparison chart.
        
        Args:
            benchmarking: Benchmarking data
            output_path: Path to save the chart
        """
        # In a real implementation, this would use actual benchmarking data
        # For now, we'll generate sample data
        models = ['Current Model', 'Previous Model', 'Industry Benchmark', 'Competitor A', 'Competitor B']
        metrics = ['AUC', 'Precision', 'Recall', 'F1 Score']
        
        # Generate random performance metrics for each model
        np.random.seed(42)  # For reproducibility
        data = np.zeros((len(models), len(metrics)))
        data[0] = np.random.uniform(0.8, 0.9, size=len(metrics))  # Current model (best)
        data[1] = np.random.uniform(0.75, 0.85, size=len(metrics))  # Previous model
        data[2] = np.random.uniform(0.7, 0.8, size=len(metrics))  # Industry benchmark
        data[3] = np.random.uniform(0.65, 0.75, size=len(metrics))  # Competitor A
        data[4] = np.random.uniform(0.6, 0.7, size=len(metrics))  # Competitor B
        
        # Create a DataFrame for easier plotting
        df = pd.DataFrame(data, index=models, columns=metrics)
        
        # Plot the benchmark comparison
        plt.figure(figsize=(12, 8))
        ax = df.plot(kind='bar', width=0.8)
        plt.xlabel('Model')
        plt.ylabel('Performance Metric')
        plt.title('Model Performance Benchmark Comparison')
        plt.legend(title='Metric')
        plt.grid(True, axis='y')
        plt.ylim(0, 1)
        
        # Add value labels on the bars
        for container in ax.containers:
            ax.bar_label(container, fmt='%.2f', padding=3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
