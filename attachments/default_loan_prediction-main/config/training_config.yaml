# Training Configuration for Loan Default Prediction Model

# Data Configuration
data:
  train_path: "data/loan_default.csv"
  test_size: 0.2  # Fraction of data to use for testing
  
# Model Configuration
model:
  type: "decision_tree"  # Options: decision_tree, random_forest, logistic_regression
  
  # Model parameters (used when tune_hyperparameters is False)
  params:
    max_depth: 7
    min_samples_split: 5
    min_samples_leaf: 2
    criterion: "gini"
    random_state: 42
  
  # Hyperparameter tuning grid (used when tune_hyperparameters is True)
  param_grid:
    max_depth: [3, 5, 7, 10, null]
    min_samples_split: [2, 5, 10]
    min_samples_leaf: [1, 2, 4]
    criterion: ["gini", "entropy"]

# Training Configuration
training:
  tune_hyperparameters: true  # Whether to perform hyperparameter tuning
  cv_folds: 5  # Number of cross-validation folds for hyperparameter tuning
  random_state: 42  # Random state for reproducibility

# Output Configuration
output:
  model_path: "models/tuned_decision_tree.pkl"
  preprocessor_path: "models/preprocessor.pkl"

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(levelname)s - %(message)s"
