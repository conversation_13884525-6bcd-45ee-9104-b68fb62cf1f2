# Makefile for Loan Default Prediction Project

.PHONY: help setup install train predict evaluate api test clean demo

# Default target
help:
	@echo "Loan Default Prediction - Available Commands:"
	@echo ""
	@echo "Setup and Installation:"
	@echo "  make setup     - Complete project setup (install deps, train model)"
	@echo "  make install   - Install Python dependencies"
	@echo ""
	@echo "Model Operations:"
	@echo "  make train     - Train the model"
	@echo "  make predict   - Make sample prediction"
	@echo "  make evaluate  - Evaluate model performance"
	@echo ""
	@echo "Development:"
	@echo "  make api       - Start API server"
	@echo "  make test      - Run tests"
	@echo "  make demo      - Run quick demo"
	@echo ""
	@echo "Utilities:"
	@echo "  make clean     - Clean generated files"
	@echo "  make format    - Format code with black"
	@echo "  make lint      - Lint code with flake8"

# Setup and installation
setup:
	@echo "🚀 Setting up Loan Default Prediction project..."
	python setup.py

install:
	@echo "📦 Installing dependencies..."
	pip install -r requirements.txt

# Model operations
train:
	@echo "🤖 Training model..."
	python src/train.py --config config/training_config.yaml

predict:
	@echo "🎯 Making sample prediction..."
	python src/predict.py --input data/sample_input.json

evaluate:
	@echo "📈 Evaluating model..."
	mkdir -p outputs
	python src/evaluate.py --data data/loan_default.csv --output outputs/

# Development
api:
	@echo "🌐 Starting API server..."
	@echo "Visit http://localhost:8000/docs for API documentation"
	python src/api.py

test:
	@echo "🧪 Running tests..."
	python -m pytest tests/ -v

demo:
	@echo "🎬 Running quick demo..."
	python quick_start.py

# Code quality
format:
	@echo "🎨 Formatting code..."
	black src/ tests/ *.py

lint:
	@echo "🔍 Linting code..."
	flake8 src/ tests/ *.py --max-line-length=100

# Utilities
clean:
	@echo "🧹 Cleaning generated files..."
	rm -rf __pycache__/
	rm -rf src/__pycache__/
	rm -rf tests/__pycache__/
	rm -rf .pytest_cache/
	rm -rf outputs/
	rm -rf logs/
	find . -name "*.pyc" -delete
	find . -name "*.pyo" -delete

# Advanced operations
retrain:
	@echo "🔄 Retraining model with hyperparameter tuning..."
	python src/train.py --config config/training_config.yaml

batch-predict:
	@echo "📊 Making batch predictions..."
	python src/predict.py --input data/loan_default.csv --output outputs/batch_predictions.csv

# Docker operations (if Docker is available)
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t loan-default-prediction .

docker-run:
	@echo "🐳 Running Docker container..."
	docker run -p 8000:8000 loan-default-prediction

# Documentation
docs:
	@echo "📚 Available documentation:"
	@echo "  - README.md - Main project documentation"
	@echo "  - docs/project_charter.md - Business objectives"
	@echo "  - docs/model_documentation.md - Technical details"
	@echo "  - docs/deployment_guide.md - Deployment instructions"

# Check project status
status:
	@echo "📊 Project Status:"
	@echo ""
	@echo "Data:"
	@if [ -f "data/loan_default.csv" ]; then echo "  ✅ Training data available"; else echo "  ❌ Training data missing"; fi
	@if [ -f "data/sample_input.json" ]; then echo "  ✅ Sample input available"; else echo "  ❌ Sample input missing"; fi
	@echo ""
	@echo "Models:"
	@if [ -f "models/tuned_decision_tree.pkl" ]; then echo "  ✅ Trained model available"; else echo "  ❌ Model not trained"; fi
	@if [ -f "models/preprocessor.pkl" ]; then echo "  ✅ Preprocessor available"; else echo "  ❌ Preprocessor missing"; fi
	@echo ""
	@echo "Configuration:"
	@if [ -f "config/training_config.yaml" ]; then echo "  ✅ Training config available"; else echo "  ❌ Training config missing"; fi
	@if [ -f "config/model_config.yaml" ]; then echo "  ✅ Model config available"; else echo "  ❌ Model config missing"; fi
