#!/usr/bin/env python3
"""
Setup script for loan default prediction project.
"""

import os
import sys
import subprocess
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(command, description):
    """Run a shell command and handle errors."""
    logger.info(f"{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    logger.info("Checking Python version...")
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8 or higher is required")
        return False
    logger.info(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def install_dependencies():
    """Install required dependencies."""
    logger.info("Installing dependencies...")
    
    # Check if requirements.txt exists
    if not os.path.exists('requirements.txt'):
        logger.error("❌ requirements.txt not found")
        return False
    
    # Install dependencies
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python packages"
    )


def create_directories():
    """Create necessary directories."""
    logger.info("Creating project directories...")
    
    directories = [
        'models',
        'logs',
        'outputs',
        'tests'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            logger.info(f"✅ Created directory: {directory}")
        else:
            logger.info(f"📁 Directory already exists: {directory}")
    
    return True


def train_initial_model():
    """Train the initial model."""
    logger.info("Training initial model...")
    
    # Check if data file exists
    if not os.path.exists('data/loan_default.csv'):
        logger.error("❌ Training data not found: data/loan_default.csv")
        return False
    
    # Check if model already exists
    if os.path.exists('models/tuned_decision_tree.pkl'):
        logger.info("📦 Model already exists, skipping training")
        return True
    
    # Train model
    return run_command(
        f"{sys.executable} src/train.py --config config/training_config.yaml",
        "Training initial model"
    )


def test_prediction():
    """Test prediction functionality."""
    logger.info("Testing prediction functionality...")
    
    # Check if model exists
    if not os.path.exists('models/tuned_decision_tree.pkl'):
        logger.error("❌ Trained model not found")
        return False
    
    # Test single prediction
    return run_command(
        f"{sys.executable} src/predict.py --input data/sample_input.json",
        "Testing prediction with sample data"
    )


def main():
    """Main setup function."""
    logger.info("🚀 Starting Loan Default Prediction setup...")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        logger.error("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Train initial model
    if not train_initial_model():
        logger.error("❌ Failed to train initial model")
        sys.exit(1)
    
    # Test prediction
    if not test_prediction():
        logger.error("❌ Failed to test prediction")
        sys.exit(1)
    
    logger.info("🎉 Setup completed successfully!")
    
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    print("\n📋 Next steps:")
    print("1. Make a prediction:")
    print("   python src/predict.py --input data/sample_input.json")
    print("\n2. Start the API server:")
    print("   python src/api.py")
    print("   Then visit: http://localhost:8000/docs")
    print("\n3. Evaluate the model:")
    print("   python src/evaluate.py --data data/loan_default.csv --output outputs/")
    print("\n4. Retrain with custom config:")
    print("   python src/train.py --config config/training_config.yaml")
    print("\n📚 Documentation: docs/")
    print("🐛 Issues: Open an issue on the repository")
    print("="*60)


if __name__ == "__main__":
    main()
