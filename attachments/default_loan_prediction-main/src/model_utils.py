"""
Model utilities for loan default prediction.
"""

import joblib
import numpy as np
import pandas as pd
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import GridSearchCV
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_auc_score
)
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LoanDefaultModel:
    """Wrapper class for loan default prediction models."""
    
    def __init__(self, model_type='decision_tree'):
        self.model_type = model_type
        self.model = None
        self.is_trained = False
        
    def _get_model(self, **params):
        """Get the specified model with parameters."""
        if self.model_type == 'decision_tree':
            return DecisionTreeClassifier(random_state=42, **params)
        elif self.model_type == 'random_forest':
            return RandomForestClassifier(random_state=42, **params)
        elif self.model_type == 'logistic_regression':
            return LogisticRegression(random_state=42, **params)
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")
    
    def train(self, X_train, y_train, **model_params):
        """Train the model."""
        logger.info(f"Training {self.model_type} model...")
        
        self.model = self._get_model(**model_params)
        self.model.fit(X_train, y_train)
        self.is_trained = True
        
        logger.info("Model training completed")
        return self
    
    def tune_hyperparameters(self, X_train, y_train, param_grid=None, cv=5):
        """Tune model hyperparameters using GridSearchCV."""
        logger.info(f"Tuning hyperparameters for {self.model_type}...")
        
        if param_grid is None:
            param_grid = self._get_default_param_grid()
        
        base_model = self._get_model()
        grid_search = GridSearchCV(
            base_model, param_grid, cv=cv, scoring='f1', n_jobs=-1, verbose=1
        )
        
        grid_search.fit(X_train, y_train)
        
        self.model = grid_search.best_estimator_
        self.is_trained = True
        
        logger.info(f"Best parameters: {grid_search.best_params_}")
        logger.info(f"Best CV score: {grid_search.best_score_:.4f}")
        
        return grid_search.best_params_
    
    def _get_default_param_grid(self):
        """Get default parameter grid for hyperparameter tuning."""
        if self.model_type == 'decision_tree':
            return {
                'max_depth': [3, 5, 7, 10, None],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'criterion': ['gini', 'entropy']
            }
        elif self.model_type == 'random_forest':
            return {
                'n_estimators': [50, 100, 200],
                'max_depth': [3, 5, 7, 10],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            }
        elif self.model_type == 'logistic_regression':
            return {
                'C': [0.1, 1, 10, 100],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear', 'saga']
            }
        else:
            return {}
    
    def predict(self, X):
        """Make predictions."""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        return self.model.predict(X)
    
    def predict_proba(self, X):
        """Get prediction probabilities."""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        return self.model.predict_proba(X)
    
    def evaluate(self, X_test, y_test):
        """Evaluate model performance."""
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        logger.info("Evaluating model performance...")
        
        # Make predictions
        y_pred = self.predict(X_test)
        y_pred_proba = self.predict_proba(X_test)[:, 1]  # Probability of default
        
        # Calculate metrics
        metrics = {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred),
            'recall': recall_score(y_test, y_pred),
            'f1_score': f1_score(y_test, y_pred),
            'roc_auc': roc_auc_score(y_test, y_pred_proba)
        }
        
        # Calculate business metrics
        default_rate = np.mean(y_pred)
        approval_rate = 1 - default_rate
        
        metrics.update({
            'predicted_default_rate': default_rate,
            'predicted_approval_rate': approval_rate
        })
        
        # Confusion matrix
        cm = confusion_matrix(y_test, y_pred)
        
        # Classification report
        report = classification_report(y_test, y_pred, target_names=['No Default', 'Default'])
        
        logger.info("Model evaluation completed")
        
        return {
            'metrics': metrics,
            'confusion_matrix': cm,
            'classification_report': report
        }
    
    def get_feature_importance(self, feature_names=None):
        """Get feature importance (for tree-based models)."""
        if not self.is_trained:
            raise ValueError("Model must be trained before getting feature importance")
        
        if hasattr(self.model, 'feature_importances_'):
            importance = self.model.feature_importances_
            
            if feature_names is not None:
                importance_df = pd.DataFrame({
                    'feature': feature_names,
                    'importance': importance
                }).sort_values('importance', ascending=False)
                return importance_df
            else:
                return importance
        else:
            logger.warning(f"Feature importance not available for {self.model_type}")
            return None
    
    def save_model(self, file_path):
        """Save the trained model."""
        if not self.is_trained:
            raise ValueError("Model must be trained before saving")
        
        model_data = {
            'model': self.model,
            'model_type': self.model_type,
            'is_trained': self.is_trained
        }
        
        joblib.dump(model_data, file_path)
        logger.info(f"Model saved to {file_path}")
    
    def load_model(self, file_path):
        """Load a trained model."""
        model_data = joblib.load(file_path)
        
        self.model = model_data['model']
        self.model_type = model_data['model_type']
        self.is_trained = model_data['is_trained']
        
        logger.info(f"Model loaded from {file_path}")


def print_evaluation_results(evaluation_results):
    """Print formatted evaluation results."""
    metrics = evaluation_results['metrics']
    
    print("\n" + "="*50)
    print("MODEL EVALUATION RESULTS")
    print("="*50)
    
    print(f"\nPerformance Metrics:")
    print(f"  Accuracy:  {metrics['accuracy']:.3f}")
    print(f"  Precision: {metrics['precision']:.3f}")
    print(f"  Recall:    {metrics['recall']:.3f}")
    print(f"  F1-Score:  {metrics['f1_score']:.3f}")
    print(f"  ROC-AUC:   {metrics['roc_auc']:.3f}")
    
    print(f"\nBusiness Metrics:")
    print(f"  Predicted Default Rate:  {metrics['predicted_default_rate']:.1%}")
    print(f"  Predicted Approval Rate: {metrics['predicted_approval_rate']:.1%}")
    
    print(f"\nConfusion Matrix:")
    print(evaluation_results['confusion_matrix'])
    
    print(f"\nClassification Report:")
    print(evaluation_results['classification_report'])


if __name__ == "__main__":
    # Example usage
    from data_preprocessing import LoanDataPreprocessor, split_data
    
    # Load and preprocess data
    preprocessor = LoanDataPreprocessor()
    data = preprocessor.load_data("../data/loan_default.csv")
    X, y = preprocessor.preprocess_training_data(data)
    X_train, X_test, y_train, y_test = split_data(X, y)
    
    # Train model
    model = LoanDefaultModel('decision_tree')
    model.train(X_train, y_train)
    
    # Evaluate model
    results = model.evaluate(X_test, y_test)
    print_evaluation_results(results)
