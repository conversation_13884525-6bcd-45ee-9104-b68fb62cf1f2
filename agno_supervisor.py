from agno.agent import Agent
from agno.models.groq import Groq
from agno.models.openrouter import OpenRouter
from agno.models.google import Gemini
from agno.models.anthropic import Claude
from agno.team import Team
import os
import json
from agno.vectordb.lancedb import LanceDb
from agno.embedder.fastembed import FastEmbedEmbedder
from agno.agent import Agent, AgentKnowledge
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication  
from email.utils import parseaddr
from agno.tools import tool
import re
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.memory.v2.memory import Memory
from dotenv import load_dotenv


load_dotenv()

memory_db = SqliteMemoryDb(table_name="memory", db_file="memoryy/memory_session.db")
memory = Memory(db=memory_db)

user_id = "<EMAIL>"
session_id = "1001"

EMAIL_ADDRESS = os.getenv('EMAIL_ADDRESS')
EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD')
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587


knowledge_base = AgentKnowledge(
    vector_db=LanceDb(
        uri="/home/<USER>/pranjal/fizanto/tmp/lancedb",
        table_name="email_memory",
        embedder=FastEmbedEmbedder(id="BAAI/bge-small-en-v1.5")
    )
)

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
# claude_api_key = os.getenv("ANTHROPIC_API_KEY")
claude_api_key = "api-key"
openrouter_api_key = "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"


# def extract_email_metadata():

#     query = "Email about Default Loan Model files"
#     results = knowledge_base.search(query)
#     content = results[0].content

#     subject_match = re.search(r"about (.+?) at", content)
#     subject = subject_match.group(1).strip()

#     message_id_match = re.search(r"identified as <(.+?)>", content)
#     message_id = message_id_match.group(1) if message_id_match else None
        
#     return subject, message_id


# def extract_email_metadata():
#     try:
#         # Access the vector database and embedder
#         vector_db = knowledge_base.vector_db
#         embedder = vector_db.embedder

#         # Generate an embedding for the query
#         query = "Email about Default Loan Model files"
#         query_embedding = embedder.embed([query])[0]  # Embed the query text

#         # Perform the search with the query embedding and limit to 1 result
#         results = vector_db.search(query=query_embedding, limit=1)

#         # Extract the first entry
#         if results and len(results) > 0:
#             content = results[0].get("text", results[0].get("content", str(results[0])))
#             print(f"Extracted content: {content}")  # Debug: Print the content to verify

#             # Parse the content using regex
#             subject_match = re.search(r"about (.+?) identified as", content)
#             subject = subject_match.group(1).strip() if subject_match else "Unknown Subject"

#             message_id_match = re.search(r"identified as <(.+?)>", content)
#             message_id = message_id_match.group(1) if message_id_match else None

#             return subject, message_id
#         else:
#             print("No matching entries found in the knowledge base.")
#             return "Unknown Subject", None
#     except Exception as e:
#         print(f"Error extracting email metadata: {e}")
#         return "Unknown Subject", None

@tool(
    name="send_email",
    description="Send an email with an attachment",
    show_result=True,
    stop_after_tool_call=True,
    cache_results=False
)
def send_email(pdf_path=None):
    """
    Use this function to send an email with an attachment.
    """

    with open('/home/<USER>/pranjal/fizanto/email_metadata.json', 'r') as f:
        data = json.load(f)

    # metadata_file = Path("/home/<USER>/pranjal/fizanto/email_metadata.json")
    # with open(metadata_file, 'r') as f:
    #         metadata = json.load(f)
        
    # subject = metadata.get('subject')
    # email_id = metadata.get('email_id')
    subject = data['email_subject']
    email_id = data['email_id']
    name, recipient = parseaddr(data['email_sender'])


    sender = "<EMAIL>"
    # recipient = "<EMAIL>"
    reply_body = "Here is the Validation Report for Default Loan Prediction Model."
    pdf_path = "/home/<USER>/pranjal/fizanto/agno-report-generation/reports/knowledge_base_output_report.pdf"

    try:
        msg = MIMEMultipart()
        msg['From'] = sender
        msg['To'] = recipient
        msg['Subject'] = "Re: " + subject
        msg['In-Reply-To'] = email_id
        msg['References'] = email_id
        msg.attach(MIMEText(reply_body, 'plain'))

        if os.path.exists(pdf_path):
            with open(pdf_path, "rb") as f:
                pdf_attachment = MIMEApplication(f.read(), _subtype="pdf")
                pdf_attachment.add_header(
                    'Content-Disposition', 'attachment', filename=os.path.basename(pdf_path)
                )
                msg.attach(pdf_attachment)
        else:
            return f"Failed to send an email: PDF file not found at {pdf_path}"

        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(EMAIL_ADDRESS, EMAIL_PASSWORD)
            server.sendmail(sender, recipient, msg.as_string())

        return "Email sent successfully"

    except Exception as e:
        return f"Failed to send an email: {str(e)}"


email_agent = Agent(
    name="Send Email",
    # description="Your only responsibility is sending emails.",
    # model=OpenRouter(id="gpt-4o"),
    model=Groq(id="llama-3.3-70b-versatile"),
    tools=[send_email],
    instructions=["Call the send_email tool to send an email"],
    show_tool_calls=True,
    markdown=True
)

greeting_agent = Agent(
    name="Greeting Agent",
    description="You are an expert in conversational responses, acting like a human colleague.",
    # description="You are an expert in greeting people",
    # model=OpenRouter(id="gpt-4o"),
    # model=Groq(id="llama-3.3-70b-versatile"),
    model=Groq(id="gemma2-9b-it"),
    instructions=["Respond as if you are a human colleague and keep responses friendly and professional.",
                  "Deflect politely to personal questions.",],
    show_tool_calls=True,
    markdown=True
)

import subprocess
import os

@tool(
    name="generate_report",
    description="Generate a report",
    show_result=True,
    stop_after_tool_call=True,
    cache_results=False
)
def generate_report():
    """
    Use this function to generate a report.
    """
    try:
        input_file = "/home/<USER>/pranjal/fizanto/knowledge_base_output.txt"
        # input_file = "/home/<USER>/pranjal/fizanto/agno-report-generation/sample_input.txt"

        command = [
            "python",
            "/home/<USER>/pranjal/fizanto/agno-report-generation/run.py",
            "--input", input_file,
            "--api-key", "12345",
        ]

        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=False,
            cwd="/home/<USER>/pranjal/fizanto/agno-report-generation"
        )

        output = f"Report generation output:\n"
        if result.stdout:
            output += f"stdout:\n{result.stdout}\n"
        if result.stderr:
            output += f"stderr:\n{result.stderr}\n"
        output += f"Exit code: {result.returncode}"

        if result.returncode != 0:
            return f"Failed to generate report"
        return f"Report generated successfully"

    except Exception as e:
        return f"Error generating report: {str(e)}"



import docker
from docker.errors import DockerException, ImageNotFound, APIError

@tool(
    name="run_analysis",
    description="Run analysis to retrieve answer for the user query",
    show_result=True,
    stop_after_tool_call=True,
    cache_results=False
)
def run_analysis(user_prompt: str) -> str:
    """
    Use this function to retrieve answer for the user query.
    """
    try:
        client = docker.from_env()

        image_name = "analysis-service" 

        client.images.get(image_name)
       
        container = client.containers.run(
            image=image_name,
            command=None,  
            volumes={
                "/home/<USER>/Downloads/fizanto/attachments": {"bind": "/app/attachments", "mode": "rw"},
                "/home/<USER>/Downloads/fizanto/.env": {"bind": "/app/.env", "mode": "ro"}
            },
            environment=[f"ANALYSIS_PROMPT={user_prompt}"],
            remove=True,  
            detach=False  
        )

        output = container.decode("utf-8") if container else "No analysis output generated."
        return output

    except ImageNotFound:
        return f"Failed to find Docker image: {image_name}"
    except APIError as e:
        return f"Failed to run Docker analysis: {str(e)}"
    except DockerException as e:
        return f"Error running Docker analysis: {str(e)}"
    except Exception as e:
        return f"Unexpected error: {str(e)}"


@tool(
    name="retrieve_filepath",
    description="Retrieve file path of the visualization",
    show_result=True,
    stop_after_tool_call=True,
    cache_results=False
)
def retrieve_filepath(user_prompt: str) -> str:
    """
    Use this function to retrieve file path of the visualization.
    """
    try:
        client = docker.from_env()

        image_name = "vis-service"  

        client.images.get(image_name)
   
        container = client.containers.run(
            image=image_name,
            command=None,  
            volumes={
                "/home/<USER>/Downloads/fizanto/attachments": {"bind": "/app/attachments", "mode": "rw"},
                "/home/<USER>/Downloads/fizanto/output": {"bind": "/app/output", "mode": "rw"},
                "/home/<USER>/Downloads/fizanto/.env": {"bind": "/app/.env", "mode": "ro"}
            },
            environment=[f"VISUALIZATION_PROMPT={user_prompt}"],
            remove=True,  
            detach=False 
        )

        output = container.decode("utf-8") if container else "No visualizations generated."
        return output

    except ImageNotFound:
        return f"Failed to find Docker image: {image_name}"
    except APIError as e:
        return f"Failed to run Docker visualization: {str(e)}"
    except DockerException as e:
        return f"Error running Docker visualization: {str(e)}"
    except Exception as e:
        return f"Unexpected error: {str(e)}"


knowledge_agent = Agent(
    name="Knowledge Agent",
    # model=Groq(id="llama-3.3-70b-versatile"),
    # model=Claude(id="claude-3-7-sonnet-20250219", api_key=claude_api_key),
    model=OpenRouter(id="gpt-4o", api_key=openrouter_api_key),
    # model=Gemini(id="gemini-2.0-flash", api_key=GEMINI_API_KEY),
    tools=[run_analysis, retrieve_filepath, generate_report],
    knowledge=knowledge_base,
    search_knowledge=True,
    instructions=["To create plot, call the retrieve_filepath tool to get filepath of the plot.",
                  "To generate a report, call the generate_report tool.",
                  "For rest of the questions, search your knowledge base for relevant answer.", 
                  "If no relevant answer is found in the knowledge base, call the run_analysis tool to retrieve the answer.",
                  ],
    show_tool_calls=True,
    markdown=True
)


supervisor_team = Team(
    name="Supervisor Team",
    mode="route",
    members=[email_agent, knowledge_agent, greeting_agent],
    memory=memory,
    enable_session_summaries=True,
    model=Groq(id="llama-3.3-70b-versatile"),
    # model=Gemini(id="gemini-2.0-flash", api_key=GEMINI_API_KEY),
    description="You are a supervisor who can analyze the query and route to the appropriate agent.",
    instructions=[
        "Route to the Greeting Agent for greetings.",
        "Route to the Email Agent only for sending emails. Not for general email inquiries.",
        "Route to Knowledge Agent for rest of the questions."
    ],
    show_tool_calls=True,
    markdown=True
)


# supervisor_team.print_response("I hope you got my email about Default Loan Model. Can you confirm that?", user_id=user_id, session_id=session_id)
# supervisor_team.print_response("What are the assumptions of the Default Loan Prediction model?", user_id=user_id, session_id=session_id)
# supervisor_team.print_response("What are the risk factors associated with the Default Loan prediction model?", user_id=user_id, session_id=session_id)
# supervisor_team.print_response("What input features are used in the Default Loan model?", user_id=user_id, session_id=session_id)
# supervisor_team.print_response("What is the average annual income?", user_id=user_id, session_id=session_id)
# supervisor_team.print_response("Create a bar plot of grade column.", user_id=user_id, session_id=session_id)
# supervisor_team.print_response("Generate a report.", user_id=user_id, session_id=session_id)



# supervisor_team.print_response("Generate a report.", user_id=user_id, session_id=session_id)
supervisor_team.print_response("Send me an email with the report.")