{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Lending Club Credit Risk Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.metrics import roc_auc_score, precision_recall_curve, roc_curve\n", "\n", "np.random.seed(42)\n", "plt.style.use('seaborn')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load raw and preprocessed data\n", "raw_df = pd.read_csv('src/data/lending_club_raw_data.csv')\n", "golden_df = pd.read_csv('src/data/lending_club_golden_data.csv')\n", "\n", "print(\"Raw dataset shape:\", raw_df.shape)\n", "print(\"Golden dataset shape:\", golden_df.shape)\n", "\n", "# Display sample of raw data\n", "print(\"\\nRaw data sample:\")\n", "raw_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare raw vs golden data\n", "print(\"Raw data columns:\", raw_df.columns.tolist())\n", "print(\"\\nGolden data additional features:\", \n", "      [col for col in golden_df.columns if col not in raw_df.columns])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Quick data overview\n", "print(\"\\nMissing values in raw data:\")\n", "print(raw_df.isnull().sum()[raw_df.isnull().sum() > 0])\n", "\n", "print(\"\\nData types:\")\n", "print(raw_df.dtypes)\n", "\n", "print(\"\\nTarget distribution:\")\n", "print(raw_df['loan_status'].value_counts(normalize=True))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic EDA plots\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# FICO Score distribution\n", "sns.histplot(data=raw_df, x='fico_score', ax=axes[0,0])\n", "axes[0,0].set_title('FICO Score Distribution')\n", "\n", "# DTI distribution\n", "sns.histplot(data=raw_df, x='dti', ax=axes[0,1])\n", "axes[0,1].set_title('DTI Distribution')\n", "\n", "# Loan amount vs Annual income\n", "sns.scatterplot(data=raw_df, x='annual_income', y='loan_amount', alpha=0.5, ax=axes[1,0])\n", "axes[1,0].set_title('<PERSON><PERSON> Amount vs Annual Income')\n", "\n", "# Default rate by grade\n", "df_default = raw_df.groupby('grade')['loan_status'].apply(lambda x: (x == 'Default').mean())\n", "sns.barplot(x=df_default.index, y=df_default.values, ax=axes[1,1])\n", "axes[1,1].set_title('Default Rate by Grade')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Examine engineered features in golden dataset\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# FICO category distribution\n", "sns.countplot(data=golden_df, x='fico_category', ax=axes[0,0])\n", "axes[0,0].set_title('FICO Category Distribution')\n", "axes[0,0].tick_params(axis='x', rotation=45)\n", "\n", "# DTI category distribution\n", "sns.countplot(data=golden_df, x='dti_category', ax=axes[0,1])\n", "axes[0,1].set_title('DTI Category Distribution')\n", "axes[0,1].tick_params(axis='x', rotation=45)\n", "\n", "# Log-transformed income distribution\n", "sns.histplot(data=golden_df, x='log_income', ax=axes[1,0])\n", "axes[1,0].set_title('Log Income Distribution')\n", "\n", "# Log-transformed loan amount distribution\n", "sns.histplot(data=golden_df, x='log_loan_amount', ax=axes[1,1])\n", "axes[1,1].set_title('Log Loan Amount Distribution')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare features for modeling using golden dataset\n", "numeric_features = ['log_income', 'log_loan_amount', 'dti', 'fico_score']\n", "categorical_features = ['term', 'grade', 'home_ownership', 'purpose', 'fico_category', 'dti_category']\n", "\n", "# Scale numeric features\n", "scaler = StandardScaler()\n", "X_numeric = scaler.fit_transform(golden_df[numeric_features])\n", "\n", "# One-hot encode categorical features\n", "X_categorical = pd.get_dummies(golden_df[categorical_features], drop_first=True)\n", "\n", "# Combine features\n", "X = np.hstack([X_numeric, X_categorical])\n", "y = (golden_df['loan_status'] == 'Default').astype(int)\n", "\n", "print(f\"Final feature matrix shape: {X.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "print(f\"Training set shape: {X_train.shape}\")\n", "print(f\"Test set shape: {X_test.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train model\n", "model = GradientBoostingClassifier(\n", "    n_estimators=100,\n", "    max_depth=5,\n", "    learning_rate=0.1,\n", "    min_samples_split=200,\n", "    random_state=42\n", ")\n", "\n", "model.fit(X_train, y_train)\n", "print(\"Model training completed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate model\n", "y_pred_proba = model.predict_proba(X_test)[:, 1]\n", "roc_auc = roc_auc_score(y_test, y_pred_proba)\n", "print(f'ROC-AUC Score: {roc_auc:.3f}')\n", "\n", "# Plot ROC curve\n", "fpr, tpr, _ = roc_curve(y_test, y_pred_proba)\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(fpr, tpr)\n", "plt.plot([0, 1], [0, 1], 'k--')\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.title(f'ROC Curve (AUC = {roc_auc:.3f})')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature importance\n", "feature_names = numeric_features + list(X_categorical.columns)\n", "importances = pd.DataFrame({\n", "    'feature': feature_names,\n", "    'importance': model.feature_importances_\n", "})\n", "importances = importances.sort_values('importance', ascending=False).head(10)\n", "\n", "plt.figure(figsize=(10, 6))\n", "sns.barplot(data=importances, x='importance', y='feature')\n", "plt.title('Top 10 Feature Importances')\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}