import lancedb
import json
import os

DB_URI = "/home/<USER>/pranjal/fizanto/tmp/lancedb"
TABLE_NAME = "email_memory"
OUTPUT_FILE = "/home/<USER>/pranjal/fizanto/knowledge_base_output.txt"

def check_stored_data():
    try:
        db = lancedb.connect(DB_URI)
        
        if TABLE_NAME not in db.table_names():
            print(f"No table '{TABLE_NAME}' found in {DB_URI}.")
            return
        
        table = db.open_table(TABLE_NAME)
        
        all_data = table.search().limit(10).to_pandas()
        
        if all_data.empty:
            print("No data stored in the knowledge base.")
            return
        
        # Open output file
        os.makedirs(os.path.dirname(OUTPUT_FILE), exist_ok=True)
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            f.write("Stored data in LanceDb:\n\n")
            print(f"Saving knowledge base output to {OUTPUT_FILE}")
            print(f"Found {len(all_data)} entries in the knowledge base (limited to 10).")
            
            for index, row in all_data.iterrows():
                try:
                    payload = json.loads(row['payload'])
                    content = payload.get('content', 'No content found')
                    # Write full content to file
                    f.write(f"Entry {index + 1}:\n{content}\n{'='*80}\n")
                    # Print preview to terminal (first 200 characters)
                    preview = content[:200] + "..." if len(content) > 200 else content
                    print(f"Entry {index + 1} (preview):\n{preview}\n{'-'*80}")
                except json.JSONDecodeError as e:
                    error_msg = f"Error parsing JSON in entry {index + 1}: {str(e)}"
                    f.write(f"Entry {index + 1}:\n{error_msg}\n{'='*80}\n")
                    print(error_msg)
        
        print(f"Full output saved to {OUTPUT_FILE}. Open the file to view all details.")
    
    except Exception as e:
        print(f"Error accessing LanceDb: {str(e)}")

if __name__ == "__main__":
    check_stored_data()