# Core FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.23.2
python-dotenv==1.0.0

# Microsoft Teams Bot Framework
botbuilder-core==4.14.8
botbuilder-schema==4.14.8

# Data processing and analysis
pandas==2.1.1
numpy==1.26.0
scipy==1.11.3

# AI and ML libraries
agno>=0.1.0
groq>=0.4.0
google-generativeai>=0.3.0
anthropic>=0.7.0

# Vector database and embeddings
lancedb==0.3.3
fastembed>=0.1.0

# Email processing
aiohttp>=3.9.0

# Jupyter notebook processing
nbformat>=5.7.0

# Background task scheduling
APScheduler==3.10.4

# HTTP requests
requests>=2.31.0

# Logging and utilities
coloredlogs>=15.0.1

# Development dependencies (optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
