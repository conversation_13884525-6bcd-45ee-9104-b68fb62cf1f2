# Unified AI Assistant

A comprehensive AI-powered assistant that consolidates email processing, file analysis, and Microsoft Teams integration into a single unified application.

## Features

- **Email Monitoring**: Automatically monitors incoming emails and processes attachments
- **AI-Powered Analysis**: Uses multiple AI agents to analyze CSV, Markdown, and Jupyter notebook files
- **Knowledge Base**: Maintains a vector database of processed information
- **Teams Integration**: Provides a Microsoft Teams bot interface for querying and report generation
- **Report Generation**: Generates comprehensive validation reports
- **Unified Architecture**: Single application replacing multiple separate services

## Architecture

The application consists of several key components:

- **Email Processor**: Monitors emails, downloads attachments, sends responses
- **Analysis Engine**: Processes files using specialized AI agents (Data Quality Analyst, Data Scientist, Risk Analyst, etc.)
- **Knowledge Base**: Vector database for storing and retrieving processed information
- **Teams Bot**: Microsoft Teams integration for user interactions
- **API Layer**: RESTful endpoints for external integrations

## Installation

### Prerequisites

- Python 3.10 or higher
- Docker (optional, for containerized deployment)
- Microsoft Teams app registration (for Teams integration)
- Required API keys (<PERSON><PERSON>q, Gemini, OpenRouter, etc.)

### Local Development Setup

1. **Clone and navigate to the project:**
   ```bash
   cd unified-ai-assistant
   ```

2. **Create virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration values
   ```

5. **Run the application:**
   ```bash
   python -m app.main
   ```

### Docker Deployment

1. **Build the Docker image:**
   ```bash
   docker build -t unified-ai-assistant .
   ```

2. **Run the container:**
   ```bash
   docker run -p 80:80 \
     -v $(pwd)/data:/app/data \
     -v $(pwd)/.env:/app/.env \
     unified-ai-assistant
   ```

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `EMAIL_ADDRESS` | Gmail address for email monitoring | Yes |
| `EMAIL_PASSWORD` | Gmail app password | Yes |
| `GROQ_API_KEY` | Groq API key for AI models | Yes |
| `GEMINI_API_KEY` | Google Gemini API key | Yes |
| `APP_ID` | Microsoft Teams app ID | Yes |
| `APP_PASSWORD` | Microsoft Teams app password | Yes |
| `OPENROUTER_API_KEY` | OpenRouter API key | No |
| `ANTHROPIC_API_KEY` | Anthropic API key | No |
| `PORT` | Application port (default: 80) | No |
| `EMAIL_CHECK_INTERVAL` | Email check interval in seconds (default: 60) | No |

### Directory Structure

```
unified-ai-assistant/
├── app/
│   ├── config/          # Configuration management
│   ├── core/            # Core business logic
│   ├── agents/          # AI agent definitions
│   ├── teams/           # Teams bot integration
│   ├── api/             # API routes
│   └── utils/           # Utility functions
├── data/
│   ├── attachments/     # Email attachments
│   ├── lancedb/         # Vector database
│   └── reports/         # Generated reports
├── requirements.txt
├── Dockerfile
└── README.md
```

## API Endpoints

### Core Endpoints

- `GET /` - Health check and service status
- `GET /health` - Detailed health check
- `POST /process-email` - Process latest unread email
- `POST /process-email-and-update-kb` - Process email and update knowledge base

### Teams Integration

- `POST /api/messages` - Microsoft Teams message handler

### Knowledge Base

- `GET /knowledge-base/status` - Knowledge base status
- `POST /knowledge-base/query` - Query knowledge base directly

## Usage

### Email Processing

The application automatically monitors the configured email account for new messages. When a new email with attachments is received:

1. Email is processed and attachments are extracted
2. Files are analyzed by specialized AI agents
3. Results are stored in the knowledge base
4. An automatic response is sent to the sender

### Teams Integration

Users can interact with the assistant through Microsoft Teams:

- **Greetings**: Handled by the Greeting Agent
- **Knowledge Queries**: Processed by the Knowledge Agent using the vector database
- **Report Requests**: Generate and send validation reports via email

### Supported File Types

- **CSV Files**: Data quality analysis and exploratory data analysis
- **Markdown Files**: Documentation review by multiple specialist agents
- **Jupyter Notebooks**: Code review and validation

## AI Agents

The system includes several specialized AI agents:

- **Data Quality Analyst**: Checks data completeness, accuracy, and consistency
- **Data Analyst**: Performs exploratory data analysis
- **Data Scientist**: Reviews model documentation for scientific validity
- **ML Engineer**: Reviews code for correctness and best practices
- **Risk Analyst**: Assesses financial risks and mitigation strategies
- **Compliance Specialist**: Ensures regulatory compliance
- **Data Governance Specialist**: Reviews data handling and governance
- **Business Analyst**: Validates business requirements alignment

## Monitoring and Logging

The application provides comprehensive logging:

- Application logs are written to `unified-ai-assistant.log`
- Structured logging with timestamps and log levels
- Health check endpoints for monitoring

## Development

### Adding New Agents

1. Create a new agent file in `app/agents/`
2. Define the agent using the agno framework
3. Add tools and instructions as needed
4. Import and integrate in the Teams bot

### Extending API

1. Add new routes in `app/api/routes.py`
2. Follow FastAPI conventions
3. Include proper error handling and logging

## Troubleshooting

### Common Issues

1. **Email Authentication Errors**: Ensure you're using an app password, not your regular Gmail password
2. **Teams Bot Not Responding**: Verify APP_ID and APP_PASSWORD are correct
3. **Knowledge Base Errors**: Check that the data directory is writable
4. **API Key Errors**: Verify all required API keys are set and valid

### Logs

Check the application logs for detailed error information:
```bash
tail -f unified-ai-assistant.log
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please refer to the project documentation or create an issue in the repository.
