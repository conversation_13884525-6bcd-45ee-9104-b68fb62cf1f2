"""
Main application module for the unified AI assistant.
"""
import asyncio
import threading
import time
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from app.config.settings import settings
from app.api.routes import router
from app.core.email_processor import email_processor
from app.core.analysis_engine import analysis_engine
from app.utils.helpers import setup_logging

# Set up logging
setup_logging()
logger = logging.getLogger(__name__)

# Global scheduler instance
scheduler = None

async def process_email_and_update_kb():
    """
    Background task to process emails and update knowledge base.
    This runs periodically to check for new emails.
    """
    try:
        logger.info("Checking for new emails...")

        # Process email
        email_result = email_processor.process_email()

        if email_result["status"] != "success":
            logger.info("No new emails to process")
            return

        logger.info(f"Processing email from: {email_result.get('email_sender', 'Unknown')}")

        # Save email metadata
        import json
        email_metadata = {
            "email_id": email_result.get("email_id", ""),
            "email_sender": email_result.get("email_sender", ""),
            "email_subject": email_result.get("email_subject", ""),
            "email_timestamp": email_result.get("email_timestamp", "")
        }

        metadata_file = f"{settings.DATA_DIR}/email_metadata.json"
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(email_metadata, f, indent=4)

        saved_files = email_result.get("saved_files", [])
        if not saved_files:
            logger.info("No attachments found in email")
            return

        # Process files with analysis engine
        analysis_result = analysis_engine.process_files(saved_files, email_metadata)

        if analysis_result["status"] == "success":
            logger.info("Email processed and knowledge base updated successfully")
        else:
            logger.error(f"Failed to process files: {analysis_result.get('message', 'Unknown error')}")

    except Exception as e:
        logger.error(f"Error in background email processing: {e}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.
    """
    global scheduler

    # Startup
    logger.info("Starting Unified AI Assistant...")

    # Validate required settings
    missing_settings = settings.validate_required_settings()
    if missing_settings:
        logger.error(f"Missing required settings: {missing_settings}")
        raise RuntimeError(f"Missing required environment variables: {', '.join(missing_settings)}")

    logger.info("All required settings validated")

    # Start email monitoring scheduler
    scheduler = AsyncIOScheduler()
    scheduler.add_job(
        process_email_and_update_kb,
        trigger=IntervalTrigger(seconds=settings.EMAIL_CHECK_INTERVAL),
        id="email_processor",
        name="Email Processing Job"
    )
    scheduler.start()
    logger.info(f"Email monitoring started (checking every {settings.EMAIL_CHECK_INTERVAL} seconds)")

    yield

    # Shutdown
    logger.info("Shutting down Unified AI Assistant...")
    if scheduler:
        scheduler.shutdown()
        logger.info("Email monitoring stopped")

def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        Configured FastAPI application instance
    """
    app = FastAPI(
        title="Unified AI Assistant",
        description="A unified application for email processing, analysis, and Teams integration",
        version="1.0.0",
        lifespan=lifespan
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include API routes
    app.include_router(router)

    return app

# Create the application instance
app = create_app()

if __name__ == "__main__":
    import uvicorn

    try:
        logger.info(f"Starting server on {settings.HOST}:{settings.PORT}")
        uvicorn.run(
            "app.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level="info"
        )
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        import traceback
        traceback.print_exc()
        raise
