#!/bin/bash

# Unified AI Assistant Production Startup Script
# This script runs the application as root on port 80

set -e

echo "🚀 Starting Unified AI Assistant in Production Mode..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root to bind to port 80"
    echo "Please run: sudo ./start-production.sh"
    exit 1
fi

# Get the directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please copy .env.example to .env and configure it."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please run ./start.sh first to set up the environment."
    exit 1
fi

# Create necessary directories
echo "📁 Creating data directories..."
mkdir -p data/attachments data/lancedb data/reports

# Set PYTHONPATH
export PYTHONPATH="$SCRIPT_DIR"

# Kill any existing process on port 80
echo "🔍 Checking for existing processes on port 80..."
if netstat -tlnp | grep -q ":80 "; then
    echo "⚠️  Found existing process on port 80, stopping it..."
    PID=$(netstat -tlnp | grep ":80 " | awk '{print $7}' | cut -d'/' -f1)
    if [ ! -z "$PID" ]; then
        kill -9 "$PID" 2>/dev/null || true
        sleep 2
    fi
fi

# Start the application
echo "🎯 Starting the Unified AI Assistant on port 80..."
echo "📊 Teams bot will be available at: http://localhost:80/api/messages"
echo "🔍 Health check available at: http://localhost:80/health"
echo "📧 Email processing will start automatically"
echo ""
echo "Press Ctrl+C to stop the application"
echo ""

# Run with the virtual environment's Python
exec "$SCRIPT_DIR/venv/bin/python" app/main.py
