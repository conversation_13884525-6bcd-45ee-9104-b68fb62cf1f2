#!/bin/bash

# Unified AI Assistant Startup Script

set -e

echo "🚀 Starting Unified AI Assistant..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please copy .env.example to .env and configure it."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install/upgrade dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating data directories..."
mkdir -p data/attachments data/lancedb data/reports

# Set PYTHONPATH
export PYTHONPATH=$(pwd)

# Start the application
echo "🎯 Starting the application on port 80..."
echo "📊 Teams bot will be available at: http://localhost:80/api/messages"
echo "🔍 Health check available at: http://localhost:80/health"
echo "📧 Email processing will start automatically"
echo ""
echo "Press Ctrl+C to stop the application"
echo ""

python -m app.main
