import os
from dotenv import load_dotenv
import pandas as pd
import ast
# from groq import Groq
from io import StringIO
import sys
import requests
import anthropic

load_dotenv()

# GROQ_API_KEY = os.getenv("GROQ_API_KEY")
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
ATTACHMENT_DIR = "/app/attachments"
OUTPUT_DIR = "/app/output"

# client = Groq(api_key=GROQ_API_KEY)
client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)

def get_csv_metadata(file_path: str) -> dict:
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        metadata = {
            "columns": list(df.columns),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "shape": df.shape
        }
        return metadata
    except Exception as e:
        raise Exception(f"Failed to read CSV: {str(e)}")

def generate_visualization_code(metadata: dict, csv_path: str, output_path: str) -> str:
    system_prompt = "You are a data analyst expert that generates valid Python code for data visualizations."
    vis_prompt = f"""
    Given the following CSV metadata, generate Python code to create visualizations using matplotlib.

    Metadata:
    - Columns: {metadata['columns']}
    - Data types: {metadata['dtypes']}
    - Shape: {metadata['shape']}

    Requirements:
    1. Import pandas as pd and matplotlib.pyplot as plt.
    2. Load the CSV using pd.read_csv('{csv_path}', encoding='utf-8').
    3. For numeric columns, generate histograms.
    4. For categorical columns, generate bar plots of value counts.
    5. Save the plots to '{output_path}' using plt.savefig().
    6. Do not call plt.show().
    7. Return only valid Python code with correct indentation and complete syntax. Do not include comments, explanations, markdown, backticks, or incomplete statements.
    8. Ensure the plot is clear with titles, labels, and legends where applicable.
    9. Ensure all print statements have closing parentheses and there are no extra parentheses.

    Example Code:
    import pandas as pd
    import matplotlib.pyplot as plt
    df = pd.read_csv('{csv_path}', encoding='utf-8')
    numeric_cols = df.select_dtypes(include=['float64', 'int64']).columns
    categorical_cols = df.select_dtypes(include=['object']).columns
    plt.figure(figsize=(10, 6))
    for i, col in enumerate(numeric_cols, 1):
        plt.subplot(len(numeric_cols), 1, i)
        plt.hist(df[col], bins=30, edgecolor='black')
        plt.title(f'Histogram of col')
        plt.xlabel(col)
        plt.ylabel('Frequency')
    plt.tight_layout()
    if len(categorical_cols) > 0:
        plt.figure(figsize=(10, 6))
        df[categorical_cols[0]].value_counts().plot(kind='bar')
        plt.title(f'Bar Plot of categorical_cols[0]')
        plt.xlabel(categorical_cols[0])
        plt.ylabel('Count')
    plt.savefig('{output_path}')
    plt.close()
    """
    try:
        response = client.messages.create(
            model="claude-3-7-sonnet-20250219",
            max_tokens=4000,
            system=system_prompt,
            messages=[
                {"role": "user", "content": vis_prompt}
            ]
        )
        code = response.content[0].text.strip()
        code_lines = code.split('\n')
        cleaned_code = []
        in_code_block = False
        for line in code_lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                continue
            if in_code_block or not line.strip().startswith(('Here is', 'This code')):
                cleaned_code.append(line)
        cleaned_code = '\n'.join(cleaned_code).strip()
        ast.parse(cleaned_code)
        return cleaned_code
    except SyntaxError as e:
        print(f"Invalid code generated by Claude:\n{cleaned_code}\nSyntax Error: {str(e)}")
        raise Exception(f"Generated code is invalid: {str(e)}")
    except Exception as e:
        raise Exception(f"Failed to generate visualization code: {str(e)}")

def generate_md_visualization_code(md_content: str, md_path: str, output_path: str) -> str:
    system_prompt = "You are a data analyst expert that analyzes Markdown content and generates valid Python code for visualizations based on the content."
    vis_prompt = f"""
    Analyze the following Markdown content and generate Python code to create a visualization using matplotlib, tailored to the content's structure.

    Markdown Content:
    {md_content}

    Requirements:
    1. Import matplotlib.pyplot as plt.
    2. Analyze the Markdown Content properly and create plots that represent the Markdown Content information visually.
    3. Save the plot to '{output_path}' using plt.savefig().
    4. Do not call plt.show().
    5. Return only valid Python code with correct indentation and complete syntax. Do not include comments, explanations, markdown, backticks, or incomplete statements.
    """
    try:
        response = client.messages.create(
            model="claude-3-7-sonnet-20250219",
            max_tokens=4000,
            system=system_prompt,
            messages=[
                {"role": "user", "content": vis_prompt}
            ]
        )
        code = response.content[0].text.strip()
        code_lines = code.split('\n')
        cleaned_code = []
        in_code_block = False
        for line in code_lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                continue
            if in_code_block or not line.strip().startswith(('Here is', 'This code')):
                cleaned_code.append(line)
        cleaned_code = '\n'.join(cleaned_code).strip()
        ast.parse(cleaned_code)
        return cleaned_code
    except SyntaxError as e:
        print(f"Invalid code generated by Claude:\n{cleaned_code}\nSyntax Error: {str(e)}")
        raise Exception(f"Generated code is invalid: {str(e)}")
    except Exception as e:
        raise Exception(f"Failed to generate Markdown visualization code: {str(e)}")

def read_md_file(file_path: str) -> str:
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        return f"Failed to read Markdown file: {str(e)}"

def visualize_md(md_path: str) -> str:
    try:
        md_content = read_md_file(md_path)
        if "Failed to read Markdown file" in md_content:
            return md_content
        md_filename = os.path.basename(md_path)
        output_path = os.path.join(OUTPUT_DIR, f"visualization_{md_filename.replace('.md', '')}.png")
        vis_code = generate_md_visualization_code(md_content, md_path, output_path)
        exec(vis_code)
        return output_path
    except Exception as e:
        return f"Error generating Markdown visualization: {str(e)}"

def visualize_csv(csv_path: str) -> str:
    try:
        metadata = get_csv_metadata(csv_path)
        csv_filename = os.path.basename(csv_path)
        output_path = os.path.join(OUTPUT_DIR, f"visualization_{csv_filename.replace('.csv', '')}.png")
        vis_code = generate_visualization_code(metadata, csv_path, output_path)
        exec(vis_code)
        return output_path
    except Exception as e:
        return f"Error generating visualization: {str(e)}"

def main():
    try:
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        response = requests.post("http://host.docker.internal:8000/process-email")
        response.raise_for_status()
        data = response.json()
        if data["status"] != "success":
            print("No unread emails or email processing failed.")
            return
        saved_files = data.get("saved_files", [])
        if not saved_files:
            print("No attachments found.")
            return
        for file_path in saved_files:
            file_path = os.path.join(ATTACHMENT_DIR, os.path.basename(file_path))
            if not os.path.exists(file_path):
                print(f"File {file_path} not found.")
                continue
            if file_path.lower().endswith('.csv'):
                output_path = visualize_csv(file_path)
                if "Error generating visualization" in output_path:
                    print(f"Failed to visualize CSV: {file_path}")
                    continue
                print(f"Visualization saved to {output_path}")
            elif file_path.lower().endswith('.md'):
                output_path = visualize_md(file_path)
                if "Error generating Markdown visualization" in output_path:
                    print(f"Failed to visualize Markdown: {file_path}")
                    continue
                print(f"Visualization saved to {output_path}")
            else:
                print(f"Skipping unsupported file: {file_path}")

    except Exception as e:
        print(f"Error processing email or files: {str(e)}")

if __name__ == "__main__":
    main()