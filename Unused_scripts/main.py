from dotenv import load_dotenv
import os
import pandas as pd
import ast
from groq import Groq

load_dotenv()
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
USER_PROMPT = os.getenv("USER_PROMPT")
CSV_PATH = "/app/input/banlk_loan.csv"

client = Groq(api_key=GROQ_API_KEY)

def get_csv_metadata(file_path: str) -> dict:
    """Extract metadata from a CSV file."""
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        metadata = {
            "columns": list(df.columns),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "shape": df.shape
        }
        return metadata
    except Exception as e:
        raise Exception(f"Failed to read CSV: {str(e)}")

def generate_analysis_code(metadata: dict, user_prompt: str) -> str:
    system_prompt = "You are a data analyst that generates valid Python code for pandas data analysis."
    analysis_prompt = f"""
    Given the following CSV metadata and user prompt, generate Python code to analyze the data.

    Metadata:
    - Columns: {metadata['columns']}
    - Data types: {metadata['dtypes']}
    - Shape: {metadata['shape']}

    User Prompt: {user_prompt}

    Requirements:
    1. Import pandas as pd.
    2. Load the CSV using pd.read_csv('/app/input/banlk_loan.csv', encoding='utf-8').
    3. Perform data analysis based on the prompt, including:
       - Summary statistics for numeric columns
       - Value counts for at least one categorical column
    4. Print the analysis results as plain text using print().
    6. Return only valid Python code with correct indentation and complete syntax. Do not include comments, explanations, markdown, backticks, or incomplete statements.
    8. Ensure all print statements have closing parentheses.

    Example Code:
    import pandas as pd
    df = pd.read_csv('/app/input/banlk_loan.csv', encoding='utf-8')
    print("Summary Statistics:")
    print(df[['Income', 'CCAvg']].describe().to_string())
    print("Personal Loan Counts:")
    print(df['Personal Loan'].value_counts().to_string())
    """
    try:
        response = client.chat.completions.create(
            model="llama3-70b-8192",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": analysis_prompt},
            ]
        )
        code = response.choices[0].message.content.strip()
        ast.parse(code)
        return code
    except SyntaxError as e:
        print(f"Invalid code generated by Groq:\n{code}\nSyntax Error: {str(e)}")
        raise Exception(f"Generated code is invalid: {str(e)}")
    except Exception as e:
        raise Exception(f"Failed to generate code: {str(e)}")

def generate_human_readable_summary(metadata: dict, analysis_output: str) -> str:
    system_prompt = "You are an expert who can summarize data analysis results in plain English."
    user_prompt = f"""
    Given the following CSV metadata and raw analysis output, create a detailed summary of the key insights. First explain what the data is about, then summarize the key findings.

    Metadata:
    - Columns: {metadata['columns']}
    - Shape: {metadata['shape']}

    Raw Analysis Output:
    {analysis_output}
    """
    try:
        response = client.chat.completions.create(
            model="llama3-70b-8192",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ]
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        raise Exception(f"Failed to generate summary: {str(e)}")

def main():
    try:
        metadata = get_csv_metadata(CSV_PATH)
    except Exception as e:
        print(f"Error reading CSV: {str(e)}")
        return
    
    try:
        analysis_code = generate_analysis_code(metadata, USER_PROMPT)
    except Exception as e:
        print(f"Error generating code: {str(e)}")
        return
    
    try:
        import sys
        from io import StringIO
        old_stdout = sys.stdout
        sys.stdout = mystdout = StringIO()
        exec(analysis_code)
        sys.stdout = old_stdout
        analysis_output = mystdout.getvalue()
        
        summary = generate_human_readable_summary(metadata, analysis_output)
        print("\n=== Analysis Summary ===")
        print(summary)
                
    except Exception as e:
        print(f"Execution error: {str(e)}")

if __name__ == "__main__":
    main()