import os
from dotenv import load_dotenv
import pandas as pd
import ast
from groq import Groq
from io import StringIO
import sys
import requests


from dotenv import load_dotenv
from agno.vectordb.lancedb import LanceDb
from agno.embedder.fastembed import FastEmbedEmbedder
from typing import List, Dict, Optional, Tuple
from agno.agent import AgentKnowledge
    

knowledge_base = AgentKnowledge(
    vector_db=LanceDb(
        uri="tmp/lancedb",
        table_name="email_memory",
        embedder=FastEmbedEmbedder(id="BAAI/bge-small-en-v1.5")
    )
)


load_dotenv()

GROQ_API_KEY = os.getenv("GROQ_API_KEY")
ATTACHMENT_DIR = "/app/attachments"
OUTPUT_DIR = "/app/output"

client = Groq(api_key=GROQ_API_KEY)

def get_csv_metadata(file_path: str) -> dict:
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        metadata = {
            "columns": list(df.columns),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "shape": df.shape
        }
        return metadata
    except Exception as e:
        raise Exception(f"Failed to read CSV: {str(e)}")

def generate_analysis_code(metadata: dict, user_prompt: str, csv_path: str) -> str:
    system_prompt = "You are a data analyst that generates valid Python code for pandas data analysis."
    analysis_prompt = f"""
    Given the following CSV metadata and user prompt, generate Python code to analyze the data.

    Metadata:
    - Columns: {metadata['columns']}
    - Data types: {metadata['dtypes']}
    - Shape: {metadata['shape']}

    User Prompt: {user_prompt}

    Requirements:
    1. Import pandas as pd.
    2. Load the CSV using pd.read_csv('{csv_path}', encoding='utf-8').
    3. Perform data analysis based on the prompt.
    4. Print the analysis results as plain text using print().
    6. Return only valid Python code with correct indentation and complete syntax. Do not include comments, explanations, markdown, backticks, or incomplete statements.
    8. Ensure all print statements have closing parentheses.

    Example Code:
    import pandas as pd
    df = pd.read_csv('{csv_path}', encoding='utf-8')
    print("Summary Statistics:")
    print(df.select_dtypes(include=['float64', 'int64']).describe().to_string())
    print("Categorical Column Counts:")
    print(df.select_dtypes(include=['object']).columns[0] + " Counts:")
    print(df.select_dtypes(include=['object']).iloc[:, 0].value_counts().to_string())
    """
    try:
        response = client.chat.completions.create(
            model="llama-3.3-70b-versatile",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": analysis_prompt},
            ]
        )
        code = response.choices[0].message.content.strip()
        # Clean the response to remove markdown and explanatory text
        code_lines = code.split('\n')
        cleaned_code = []
        in_code_block = False
        for line in code_lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                continue
            if in_code_block or not line.strip().startswith(('Here is', 'This code')):
                cleaned_code.append(line)
        cleaned_code = '\n'.join(cleaned_code).strip()
        # Validate the generated code
        ast.parse(cleaned_code)
        return cleaned_code
    except SyntaxError as e:
        print(f"Invalid code generated by Groq:\n{cleaned_code}\nSyntax Error: {str(e)}")
        raise Exception(f"Generated code is invalid: {str(e)}")
    except Exception as e:
        raise Exception(f"Failed to generate code: {str(e)}")

def generate_human_readable_summary(metadata: dict, analysis_output: str) -> str:
    system_prompt = "You are an expert who can describe data analysis results in plain English in a detailed manner."
    user_prompt = f"""
    Given the following CSV metadata and raw analysis output, create a detailed description of the key insights.

    Metadata:
    - Columns: {metadata['columns']}
    - Shape: {metadata['shape']}

    Raw Analysis Output:
    {analysis_output}
    """
    try:
        response = client.chat.completions.create(
            model="llama3-70b-8192",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ]
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        raise Exception(f"Failed to generate summary: {str(e)}")

def analyze_csv(csv_path: str, user_prompt: str) -> str:
    try:
        metadata = get_csv_metadata(csv_path)
        analysis_code = generate_analysis_code(metadata, user_prompt, csv_path)
        old_stdout = sys.stdout
        sys.stdout = mystdout = StringIO()
        exec(analysis_code)
        sys.stdout = old_stdout
        analysis_output = mystdout.getvalue()
        summary = generate_human_readable_summary(metadata, analysis_output)
        return summary
    except Exception as e:
        return f"Error analyzing CSV: {str(e)}"
    
    
def read_md_file(file_path: str) -> str:
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        return f"Failed to read Markdown file: {str(e)}"
    
import google.generativeai as genai

google_api_key = os.getenv("GEMINI_API_KEY")

genai.configure(api_key=google_api_key)  

gemini_model = genai.GenerativeModel('gemini-1.5-flash')  

def summarize_md_file(md_content: str) -> str:
    try:
        prompt = f"Summarize this in a detailed manner: {md_content}"
        response = gemini_model.generate_content(prompt)
        return response.text.strip()
    except Exception as e:
        return f"Failed to summarize Markdown file: {str(e)}"

def main():
    try:
        analysis_prompt = os.getenv(
            "ANALYSIS_PROMPT", 
        )
        response = requests.post("http://host.docker.internal:8000/process-email")
        response.raise_for_status()
        data = response.json()
        if data["status"] != "success":
            print("No unread emails or email processing failed.")
            return
        saved_files = data.get("saved_files", [])
        if not saved_files:
            print("No attachments found.")
            return

        csv_files = []
        md_files = []

        for file_path in saved_files:
            file_path = os.path.join(ATTACHMENT_DIR, os.path.basename(file_path))
            if not os.path.exists(file_path):
                print(f"File {file_path} not found.")
                continue
            file_name = os.path.basename(file_path)
            if file_path.lower().endswith('.csv'):
                summary = analyze_csv(file_path, analysis_prompt)
                csv_files.append({"name": file_name, "summary": summary})
            elif file_path.lower().endswith('.md'):
                md_content = read_md_file(file_path)
                md_summary = summarize_md_file(md_content)
                md_files.append({"name": file_name, "summary": md_summary})
            else:
                print(f"Skipping unsupported file: {file_path}")

        if csv_files or md_files:
            email_metadata_message = (
                f"Received an email from {data['email_sender']} at {data['email_timestamp']} "
                f"with subject {data['email_subject']} and body {data['email_body']} "
                f"identified as {data['email_id']}. "
                f"There were {len(csv_files)} CSV files and {len(md_files)} Markdown files. "
            )
            for csv in csv_files:
                email_metadata_message += f"The detailed analytics of {csv['name']} is {csv['summary']}. "
            for md in md_files:
                email_metadata_message += f"The {md['name']} contains {md['summary']}. "
            knowledge_base.load_text(email_metadata_message)

    except Exception as e:
        print(f"Error processing email or files: {str(e)}")

    except Exception as e:
        print(f"Error processing email or files: {str(e)}")

if __name__ == "__main__":
    main()