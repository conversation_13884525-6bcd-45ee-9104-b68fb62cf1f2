import os
from dotenv import load_dotenv
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import ast
from groq import Groq
from io import StringIO
import sys
import anthropic


load_dotenv()
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")


GROQ_API_KEY = os.getenv("GROQ_API_KEY")
ATTACHMENT_DIR = "/app/attachments"
OUTPUT_DIR = "/app/output"

client_groq = Groq(api_key=GROQ_API_KEY)

client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)

def get_csv_metadata(file_path: str) -> dict:
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        metadata = {
            "columns": list(df.columns),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "shape": df.shape
        }
        return metadata
    except Exception as e:
        raise Exception(f"Failed to read CSV: {str(e)}")

def generate_visualization_code(metadata: dict, user_prompt: str, csv_path: str, output_path: str) -> str:
    system_prompt = "You are a data analyst expert that generates valid Python code for data visualizations."
    vis_prompt = f"""
    Given the following CSV metadata and user prompt, generate Python code to create plot using matplotlib.

    Metadata:
    - Columns: {metadata['columns']}
    - Data types: {metadata['dtypes']}
    - Shape: {metadata['shape']}

    User Prompt: {user_prompt}

    Requirements:
    1. Import pandas as pd, matplotlib.pyplot as plt and numpy as np.
    2. Load the CSV using pd.read_csv('{csv_path}', encoding='utf-8').
    3. Generate visualization plot based on the user prompt and information from Metadata.
    5. Save the plot to '{output_path}' using plt.savefig().
    6. Do not call plt.show().
    7. Return only valid Python code with correct indentation and complete syntax. Do not include comments, explanations, markdown, backticks, or incomplete statements.
    8. Ensure the plot is clear with titles, labels, and legends where applicable.
    9. Ensure all print statements have closing parentheses and there are no extra parentheses.
    """
    try:
        # response = client_groq.chat.completions.create(
        #     model="llama-3.3-70b-versatile",
        #     messages=[
        #         {"role": "system", "content": system_prompt},
        #         {"role": "user", "content": vis_prompt},
        #     ]
        # )
        # code = response.choices[0].message.content.strip()
        response = client.messages.create(
            model="claude-3-7-sonnet-20250219",
            max_tokens=4000,
            system=system_prompt,
            messages=[
                {"role": "user", "content": vis_prompt}
            ]
        )
        code = response.content[0].text.strip()
        code_lines = code.split('\n')
        cleaned_code = []
        in_code_block = False
        for line in code_lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                continue
            if in_code_block or not line.strip().startswith(('Here is', 'This code')):
                cleaned_code.append(line)
        cleaned_code = '\n'.join(cleaned_code).strip()
        ast.parse(cleaned_code)
        # print(f"Generated code:\n{cleaned_code}")
        return cleaned_code
    except SyntaxError as e:
        print(f"Invalid code generated by Claude:\n{cleaned_code}\nSyntax Error: {str(e)}")
        raise Exception(f"Generated code is invalid: {str(e)}")
    except Exception as e:
        raise Exception(f"Failed to generate visualization code: {str(e)}")

    
# def visualize_csv(csv_path: str, user_prompt: str) -> str:
#     try:
#         metadata = get_csv_metadata(csv_path)
#         csv_filename = os.path.basename(csv_path)
#         output_path = os.path.join(OUTPUT_DIR, f"visualization_{csv_filename.replace('.csv', '')}.png")
#         vis_code = generate_visualization_code(metadata, user_prompt, csv_path, output_path)
#         exec(vis_code)
#         return output_path
#     except Exception as e:
#         return f"Error generating visualization: {str(e)}"
    

def visualize_csv(csv_path: str, user_prompt: str) -> str:
    try:
        metadata = get_csv_metadata(csv_path)
        csv_filename = os.path.basename(csv_path)
        output_path = os.path.join(OUTPUT_DIR, f"visualization_{csv_filename.replace('.csv', '')}.png")
        vis_code = generate_visualization_code(metadata, user_prompt, csv_path, output_path)
        exec(vis_code)
        if not os.path.exists(output_path):
            raise FileNotFoundError(f"Plot not saved at {output_path}")
        return output_path
    except Exception as e:
        print(f"Error in visualize_csv for {csv_path}: {str(e)}")
        return f"Error generating visualization: {str(e)}"

def main():
    try:
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        visualization_prompt = os.getenv("VISUALIZATION_PROMPT", "")
        if not visualization_prompt:
            print("No VISUALIZATION_PROMPT provided.")
            return

        if not os.path.exists(ATTACHMENT_DIR):
            print(f"Attachment directory {ATTACHMENT_DIR} does not exist.")
            return

        for file_name in os.listdir(ATTACHMENT_DIR):
            file_path = os.path.join(ATTACHMENT_DIR, file_name)
            if file_path.lower().endswith('.csv'):
                output_path = visualize_csv(file_path, visualization_prompt)
                if "Error generating visualization" in output_path:
                    print(f"Failed to visualize: {file_path}")
                    continue
                print(output_path)


    except Exception as e:
        print(f"Error processing files: {str(e)}")

if __name__ == "__main__":
    main()