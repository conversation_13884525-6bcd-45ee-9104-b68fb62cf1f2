import os
from dotenv import load_dotenv
import pandas as pd
import ast
from groq import Groq
from io import StringIO
import sys
import anthropic


load_dotenv()

GROQ_API_KEY = os.getenv("GROQ_API_KEY")
ATTACHMENT_DIR = "/app/attachments"

ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
client_groq = Groq(api_key=GROQ_API_KEY)
client = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)


def get_csv_metadata(file_path: str) -> dict:
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        metadata = {
            "columns": list(df.columns),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "shape": df.shape
        }
        return metadata
    except Exception as e:
        raise Exception(f"Failed to read CSV: {str(e)}")

def generate_analysis_code(metadata: dict, user_prompt: str, csv_path: str) -> str:
    system_prompt = "You are a data analyst that generates valid Python code for pandas data analysis."
    analysis_prompt = f"""
    Given the following CSV metadata and user prompt, generate Python code to accomplish the task specified by the user.

    Metadata:
    - Columns: {metadata['columns']}
    - Data types: {metadata['dtypes']}
    - Shape: {metadata['shape']}

    User Prompt: {user_prompt}

    Requirements:
    1. Import pandas as pd.
    2. Load the CSV using pd.read_csv('{csv_path}', encoding='utf-8').
    3. Perform the task specified in the user prompt.
    4. Print the results as plain text using print().
    6. Return only valid Python code with correct indentation and complete syntax. Do not include comments, explanations, markdown, backticks, or incomplete statements.
    8. Ensure all print statements have closing parentheses.
    """
    try:
        # response = client_groq.chat.completions.create(
        #     model="llama-3.3-70b-versatile",
        #     messages=[
        #         {"role": "system", "content": system_prompt},
        #         {"role": "user", "content": analysis_prompt},
        #     ]
        # )
        # code = response.choices[0].message.content.strip()
        response = client.messages.create(
            model="claude-3-7-sonnet-20250219",
            max_tokens=4000,
            system=system_prompt,
            messages=[
                {"role": "user", "content": analysis_prompt}
            ]
        )
        code = response.content[0].text.strip()
        # Clean the response to remove markdown and explanatory text
        code_lines = code.split('\n')
        cleaned_code = []
        in_code_block = False
        for line in code_lines:
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                continue
            if in_code_block or not line.strip().startswith(('Here is', 'This code')):
                cleaned_code.append(line)
        cleaned_code = '\n'.join(cleaned_code).strip()
        # Validate the generated code
        ast.parse(cleaned_code)
        return cleaned_code
    except SyntaxError as e:
        print(f"Invalid code generated by Groq:\n{cleaned_code}\nSyntax Error: {str(e)}")
        raise Exception(f"Generated code is invalid: {str(e)}")
    except Exception as e:
        raise Exception(f"Failed to generate code: {str(e)}")
    
def generate_summary(analysis_output: str) -> str:
    system_prompt = "You are an expert who can describe data analysis output in plain English in a detailed manner."
    user_prompt = f"""
    Given the following raw analysis output, create a detailed summary.

    Raw Analysis Output:
    {analysis_output}
    """
    try:
        response = client_groq.chat.completions.create(
            model="gemma2-9b-it",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ]
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        raise Exception(f"Failed to generate summary: {str(e)}")


def analyze_csv(csv_path: str, user_prompt: str) -> str:
    try:
        metadata = get_csv_metadata(csv_path)
        analysis_code = generate_analysis_code(metadata, user_prompt, csv_path)
        old_stdout = sys.stdout
        sys.stdout = mystdout = StringIO()
        exec(analysis_code)
        sys.stdout = old_stdout
        output = mystdout.getvalue()
        # summary = generate_summary(output)
        return output
    except Exception as e:
        return f"Error analyzing CSV: {str(e)}"

def main():
    try:
        analysis_prompt = os.getenv("ANALYSIS_PROMPT", "")
        if not analysis_prompt:
            print("No ANALYSIS_PROMPT provided.")
            return

        if not os.path.exists(ATTACHMENT_DIR):
            print(f"Attachment directory {ATTACHMENT_DIR} does not exist.")
            return

        for file_name in os.listdir(ATTACHMENT_DIR):
            file_path = os.path.join(ATTACHMENT_DIR, file_name)
            if file_path.lower().endswith('.csv'):
                summary = analyze_csv(file_path, analysis_prompt)
                print(summary)

    except Exception as e:
        print(f"Error processing files: {str(e)}")

if __name__ == "__main__":
    main()