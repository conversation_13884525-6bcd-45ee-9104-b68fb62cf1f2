import os
from dotenv import load_dotenv
import pandas as pd
import ast
from groq import Groq
import anthropic
from io import StringIO
import sys
import requests
import google.generativeai as genai
import nbformat
from agno.vectordb.lancedb import LanceDb
from agno.embedder.fastembed import FastEmbedEmbedder
from agno.agent import AgentKnowledge

# Initialize knowledge base
# knowledge_base = AgentKnowledge(
#     vector_db=LanceDb(
#         uri="/app/tmp/lancedb",
#         table_name="email_memory",
#         embedder=FastEmbedEmbedder(id="BAAI/bge-small-en-v1.5")
#     )
# )

knowledge_base = AgentKnowledge(
    vector_db=LanceDb(
        uri="/home/<USER>/pranjal/fizanto/tmp/lancedb",  # Updated to host path
        table_name="email_memory",
        embedder=FastEmbedEmbedder(id="BAAI/bge-small-en-v1.5")
    )
)


# Load environment variables
load_dotenv()
GROQ_API_KEY = "********************************************************"
# ATTACHMENT_DIR = "/app/attachments"
# OUTPUT_DIR = "/app/output"

ATTACHMENT_DIR = "/home/<USER>/pranjal/fizanto/attachments"
OUTPUT_DIR = "/home/<USER>/pranjal/fizanto/output"

# Initialize Groq client
client_groq = Groq(api_key=GROQ_API_KEY)


# ANTHROPIC_API_KEY = "************************************************************************************************************"
# client_claude = anthropic.Anthropic(api_key=ANTHROPIC_API_KEY)

# Initialize Gemini client
google_api_key = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=google_api_key)
gemini_model = genai.GenerativeModel('gemini-1.5-flash')

def get_csv_metadata(file_path: str) -> dict:
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        metadata = {
            "columns": list(df.columns),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "shape": df.shape
        }
        return metadata
    except Exception as e:
        raise Exception(f"Failed to read CSV: {str(e)}")

# Role-specific functions
def data_quality_analyst(csv_path: str, metadata: dict) -> str:
    try: 
        # Generate analysis code
        system_prompt = "You are a data quality analyst generating Python code to check data integrity using pandas."
        analysis_prompt = f"""
        Given the CSV metadata, generate valid Python code to check for completeness, accuracy, and consistency.

        Metadata:
        - Columns: {metadata['columns']}
        - Data types: {metadata['dtypes']}
        - Shape: {metadata['shape']}

        Analyze:
        1. Missing values (count and percentage per column).
        2. Duplicate rows (count and list if any).
        3. Outliers in numeric columns.

        Requirements:
        1. Import pandas as pd.
        2. Load the CSV using pd.read_csv('{csv_path}', encoding='utf-8').
        3. Return only valid Python code with correct indentation.
        4. Ensure all print statements have closing parentheses.
        """

        response = client_groq.chat.completions.create(
            model="llama-3.3-70b-versatile",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": analysis_prompt},
            ]
        )
        code = response.choices[0].message.content.strip()

        # response = client_claude.messages.create(
        #     model="claude-3-7-sonnet-20250219",
        #     max_tokens=4000,
        #     system=system_prompt,
        #     messages=[
        #         {"role": "user", "content": analysis_prompt}
        #     ]
        # )
        # code = response.content[0].text.strip()

        code_lines = code.split('\n')
        cleaned_code = [line for line in code_lines if not line.strip().startswith(('```', 'Here is', 'This code'))]
        cleaned_code = '\n'.join(cleaned_code).strip()
        ast.parse(cleaned_code)

        # Execute code
        old_stdout = sys.stdout
        sys.stdout = mystdout = StringIO()
        exec(cleaned_code)
        sys.stdout = old_stdout
        analysis_output = mystdout.getvalue()

        # Summarize
        summary_prompt = f"As a Data Quality Analyst, provide a detailed summary of the following data quality output: {analysis_output}"
        response = gemini_model.generate_content(summary_prompt)
        return response.text.strip()
    except Exception as e:
        return f"Error analyzing CSV for Data Quality Analyst: {str(e)}"

def data_analyst(csv_path: str, metadata: dict) -> str:
    try:
        system_prompt = "You are a data analyst generating Python pandas code for exploratory data analysis."
        analysis_prompt = f"""
        Given the CSV metadata, generate Python code for thorough exploratory data analysis using pandas.

        Metadata:
        - Columns: {metadata['columns']}
        - Data types: {metadata['dtypes']}
        - Shape: {metadata['shape']}

        Include:
        1. Summary statistics (mean, median, std) for numeric columns.
        2. Value counts for categorical columns.
        3. Correlation matrix for numeric columns.
        4. Distribution analysis (e.g., skewness, kurtosis).

        Requirements:
        1. Import pandas as pd.
        2. Load the CSV using pd.read_csv('{csv_path}', encoding='utf-8').
        3. Return only valid Python code with correct indentation.
        4. Ensure all print statements have closing parentheses.
        """
        response = client_groq.chat.completions.create(
            model="llama-3.3-70b-versatile",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": analysis_prompt},
            ]
        )
        code = response.choices[0].message.content.strip()

        # response = client_claude.messages.create(
        #     model="claude-3-7-sonnet-20250219",
        #     max_tokens=4000,
        #     system=system_prompt,
        #     messages=[
        #         {"role": "user", "content": analysis_prompt}
        #     ]
        # )
        # code = response.content[0].text.strip()

        code_lines = code.split('\n')
        cleaned_code = [line for line in code_lines if not line.strip().startswith(('```', 'Here is', 'This code'))]
        cleaned_code = '\n'.join(cleaned_code).strip()
        ast.parse(cleaned_code)

        # Execute code
        old_stdout = sys.stdout
        sys.stdout = mystdout = StringIO()
        exec(cleaned_code)
        sys.stdout = old_stdout
        analysis_output = mystdout.getvalue()

        # Summarize
        summary_prompt = f"As a Data Analyst, provide a detailed summary of the following exploratory data analysis: {analysis_output}"
        response = gemini_model.generate_content(summary_prompt)
        return response.text.strip()
    except Exception as e:
        return f"Error analyzing CSV for Data Analyst: {str(e)}"
    

def data_scientist(md_path: str) -> str:
    try:
        # Read Markdown
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # Summarize
        prompt = """
        As a Data Scientist, review the following model documentation for:
        1. Clarity and completeness of model description.
        2. Scientific validity of assumptions and methodology.
        3. Appropriateness of evaluation metrics.
        Provide a detailed summary of findings.
        
        Documentation:
        {md_content}
        """
        response = gemini_model.generate_content(prompt.format(md_content=md_content))
        return response.text.strip()
    except Exception as e:
        return f"Error reviewing documentation for Data Scientist: {str(e)}"

def ml_engineer(ipynb_path: str) -> str:
    try:
        # Read IPython notebook
        with open(ipynb_path, 'r', encoding='utf-8') as f:
            notebook = nbformat.read(f, as_version=4)
        code_cells = [cell['source'] for cell in notebook.cells if cell.cell_type == 'code']
        notebook_content = '\n'.join(code_cells)
        
        # Analyze code directly with Gemini
        prompt = f"""
        As an ML Engineer, review the following Jupyter notebook code for correctness:

        Code:
        ```python
        {notebook_content}
        ```

        Check for:
        1. Syntax errors (e.g., invalid Python syntax).
        2. Logical errors (e.g., undefined variables, incorrect ML algorithm usage).
        3. Adherence to best practices (e.g., proper imports, modular code).
        
        Provide a detailed report summarizing any issues found or confirming the code is correct.
        """
        response = gemini_model.generate_content(prompt)
        return response.text.strip()
    except Exception as e:
        return f"Error analyzing IPython notebook for ML Engineer: {str(e)}"

def risk_analyst(md_path: str) -> str:
    try:
        # Read Markdown
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # Summarize
        prompt = """
        As a Risk Analyst, assess the following model documentation for financial risks:
        1. Potential model errors and their financial impact.
        2. Risk exposure under different scenarios.
        3. Mitigation strategies described.
        Provide a detailed risk assessment report.
        
        Documentation:
        {md_content}
        """
        response = gemini_model.generate_content(prompt.format(md_content=md_content))
        return response.text.strip()
    except Exception as e:
        return f"Error assessing risks for Risk Analyst: {str(e)}"

def compliance_specialist(md_path: str) -> str:
    try:
        # Read Markdown
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # Summarize
        prompt = """
        As a Compliance Specialist, review the following model documentation for:
        1. Compliance with SR 11-7 guidelines (e.g., validation processes, documentation).
        2. Soundness of theoretical foundations and assumptions.
        3. Adherence to bank policies on data usage and model outputs.
        Provide a detailed compliance report.
        
        Documentation:
        {md_content}
        """
        response = gemini_model.generate_content(prompt.format(md_content=md_content))
        return response.text.strip()
    except Exception as e:
        return f"Error verifying compliance for Compliance Specialist: {str(e)}"
    

def data_governance_specialist(md_path: str, metadata: dict) -> str:
    try:
        # Read CSV metadata and Markdown
        csv_summary = f"CSV Metadata:\n- Columns: {metadata['columns']}\n- Data types: {metadata['dtypes']}\n- Shape: {metadata['shape']}"
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        prompt = f"""
        As a Data Governance Specialist, review the following CSV metadata and model documentation to:
        1. Validate that the data aligns with the model’s theoretical approach.
        2. Ensure data handling and access controls meet bank policies (e.g., data privacy, encryption, access restrictions, compliance with GDPR or bank regulations).

        {csv_summary}

        Model Documentation:
        {md_content}

        Provide a detailed report summarizing:
        - Alignment of data features with the model’s theoretical assumptions.
        - Compliance of data handling practices with bank policies and regulations.
        - Any gaps or recommendations for improvement.
        """
        response = gemini_model.generate_content(prompt)
        return response.text.strip()
    except Exception as e:
        return f"Error analyzing data governance for Data Governance Specialist: {str(e)}"

def business_analyst(md_path: str) -> str:
    try:
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        prompt = """
        As a Business Analyst, review the following model documentation to ensure the credit risk model meets business requirements:
        1. Verify that the model supports key business objectives.
        2. Check alignment with stakeholder needs (e.g., usability for loan officers, interpretability).
        3. Identify any gaps in meeting business goals.

        Documentation:
        {md_content}

        Provide a detailed report summarizing:
        - Alignment with business objectives and stakeholder needs.
        - Any gaps or recommendations for improvement.
        """
        response = gemini_model.generate_content(prompt.format(md_content=md_content))
        return response.text.strip()
    except Exception as e:
        return f"Error analyzing business requirements for Business Analyst: {str(e)}"


def main():
    try:
        response = requests.post("http://localhost:8000/process-email")
        response.raise_for_status()
        data = response.json()
        if data["status"] != "success":
            print("No unread emails or email processing failed.")
            return

        saved_files = data.get("saved_files", [])
        email_sender = data.get("email_sender", "")
        email_timestamp = data.get("email_timestamp", "")
        email_subject = data.get("email_subject", "")
        email_id = data.get("email_id", "")

        if not saved_files:
            print("No attachments found.")
            return

        csv_files = []
        md_files = []
        ipynb_files = []
        results = []

        for file_path in saved_files:
            file_path = os.path.join(ATTACHMENT_DIR, os.path.basename(file_path))
            if not os.path.exists(file_path):
                print(f"File {file_path} not found.")
                continue
            file_name = os.path.basename(file_path)
            if file_path.lower().endswith('.csv'):
                csv_files.append({"name": file_name, "path": file_path})
            elif file_path.lower().endswith('.md'):
                md_files.append({"name": file_name, "path": file_path})
            elif file_path.lower().endswith('.ipynb'):
                ipynb_files.append({"name": file_name, "path": file_path})
            else:
                print(f"Skipping unsupported file: {file_path}")

        for csv in csv_files:
            metadata = get_csv_metadata(csv["path"])
            results.append({
                "role": "Data Quality Analyst",
                "file": csv["name"],
                "result": data_quality_analyst(csv["path"], metadata)
            })
            results.append({
                "role": "Data Analyst",
                "file": csv["name"],
                "result": data_analyst(csv["path"], metadata)
            })

        for md in md_files:
            metadata = get_csv_metadata(csv_files[0]["path"]) if csv_files else {"columns": [], "dtypes": {}, "shape": (0, 0)}
            results.append({
                "role": "Data Scientist",
                "file": md["name"],
                "result": data_scientist(md["path"])
            })
            results.append({
                "role": "Risk Analyst",
                "file": md["name"],
                "result": risk_analyst(md["path"])
            })
            results.append({
                "role": "Compliance Specialist",
                "file": md["name"],
                "result": compliance_specialist(md["path"])
            })
            results.append({
                "role": "Data Governance Specialist",
                "file": md["name"],
                "result": data_governance_specialist(md["path"], metadata)
            })
            results.append({
                "role": "Business Analyst",
                "file": md["name"],
                "result": business_analyst(md["path"])
            })

        for ipynb in ipynb_files:
            results.append({
                "role": "ML Engineer",
                "file": ipynb["name"],
                "result": ml_engineer(ipynb["path"])
            })

        if results:
            email_metadata_message = (
                f"Received an email from {email_sender} at {email_timestamp} "
                f"with subject {email_subject} identified as {email_id}. "
                f"Processed {len(csv_files)} CSV files, {len(md_files)} Markdown files, "
                f"and {len(ipynb_files)} IPython notebooks. "
            )
            for result in results:
                email_metadata_message += (
                    f"{result['role']}: {result['result']}. "
                )
            knowledge_base.load_text(email_metadata_message)
            print("Knowledge base updated with role-specific analyses.")

    except Exception as e:
        print(f"Error processing email or files: {str(e)}")

if __name__ == "__main__":
    main()



# def main():
#     try:
#         response = requests.post("http://host.docker.internal:8000/process-email")
#         # response = requests.post("https://email-automation-app.fly.dev/process-email")
#         response.raise_for_status()
#         data = response.json()
#         if data["status"] != "success":
#             print("No unread emails or email processing failed.")
#             return

#         saved_files = data.get("saved_files", [])
#         email_sender = data.get("email_sender", "")
#         email_timestamp = data.get("email_timestamp", "")
#         email_subject = data.get("email_subject", "")
#         email_id = data.get("email_id", "")

#         if not saved_files:
#             print("No attachments found.")
#             return

#         csv_files = []
#         md_files = []
#         ipynb_files = []
#         results = []

#         # Process attachments
#         for file_path in saved_files:
#             file_path = os.path.join(ATTACHMENT_DIR, os.path.basename(file_path))
#             if not os.path.exists(file_path):
#                 print(f"File {file_path} not found.")
#                 continue
#             file_name = os.path.basename(file_path)
#             if file_path.lower().endswith('.csv'):
#                 csv_files.append({"name": file_name, "path": file_path})
#             elif file_path.lower().endswith('.md'):
#                 md_files.append({"name": file_name, "path": file_path})
#             elif file_path.lower().endswith('.ipynb'):
#                 ipynb_files.append({"name": file_name, "path": file_path})
#             else:
#                 print(f"Skipping unsupported file: {file_path}")

#         # Run role-specific analyses
#         for csv in csv_files:
#             metadata = get_csv_metadata(csv["path"])
#             results.append({
#                 "role": "Data Quality Analyst",
#                 "file": csv["name"],
#                 "result": data_quality_analyst(csv["path"], metadata)
#             })
#             results.append({
#                 "role": "Data Analyst",
#                 "file": csv["name"],
#                 "result": data_analyst(csv["path"], metadata)
#             })

#         for md in md_files:
#             metadata = get_csv_metadata(csv["path"])
#             results.append({
#                 "role": "Data Scientist",
#                 "file": md["name"],
#                 "result": data_scientist(md["path"])
#             })
#             results.append({
#                 "role": "Risk Analyst",
#                 "file": md["name"],
#                 "result": risk_analyst(md["path"])
#             })
#             results.append({
#                 "role": "Compliance Specialist",
#                 "file": md["name"],
#                 "result": compliance_specialist(md["path"])
#             })
#             results.append({
#                 "role": "Data Governance Specialist",
#                 "file": md["name"],
#                 "result": data_governance_specialist(md["path"], metadata)
#             })
#             results.append({
#                 "role": "Business Analyst",
#                 "file": md["name"],
#                 "result": business_analyst(md["path"])
#             })

#         for ipynb in ipynb_files:
#             results.append({
#                 "role": "ML Engineer",
#                 "file": ipynb["name"],
#                 "result": ml_engineer(ipynb["path"])
#             })

#         # Load results into knowledge base
#         if results:
#             email_metadata_message = (
#                 f"Received an email from {email_sender} at {email_timestamp} "
#                 f"with subject {email_subject} identified as {email_id}. "
#                 f"Processed {len(csv_files)} CSV files, {len(md_files)} Markdown files, "
#                 f"and {len(ipynb_files)} IPython notebooks. "
#             )
#             for result in results:
#                 email_metadata_message += (
#                     f"{result['role']}: {result['result']}. "
#                     # f"{result['role']} analyzed {result['file']}: {result['result']}. "
#                 )
#             knowledge_base.load_text(email_metadata_message)
#             print("Knowledge base updated with role-specific analyses.")

#     except Exception as e:
#         print(f"Error processing email or files: {str(e)}")

# if __name__ == "__main__":
#     main()